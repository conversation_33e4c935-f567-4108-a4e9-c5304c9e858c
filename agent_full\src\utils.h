#ifndef UTILS_H
#define UTILS_H

#include "agent.h"

// Utility functions
uint64_t get_timestamp_ms(void);
void format_timestamp(char* buffer, size_t size);
char* create_json_message(const char* type, const char* agent_id, const char* data);
void free_json_message(char* message);
void get_system_info(char* buffer, size_t size);

// Base64 and crypto functions
unsigned char* base64_decode(const char* input, size_t* output_length);
char* calculate_file_checksum(const unsigned char* data, size_t length);
int verify_checksum(const unsigned char* data, size_t length, const char* expected_checksum);

#endif // UTILS_H