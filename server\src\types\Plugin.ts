export interface PluginInfo {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  filePath: string;
  fileSize: number;
  checksum: string;
  signature: string;
  uploadTime: Date;
  dependencies: string[];
  targetSystems: string[];
  category: PluginCategory;
  enabled: boolean;
  auditStatus?: PluginAuditStatus;
}

export enum PluginCategory {
  CAMERA = 'camera',
  FILE_MONITOR = 'file_monitor',
  SYSTEM_INFO = 'system_info',
  NETWORK = 'network',
  CUSTOM = 'custom'
}

export interface PluginBundle {
  id: string;
  name: string;
  plugins: string[];
  description: string;
  createdTime: Date;
}

export interface PluginExecutionResult {
  pluginId: string;
  agentId: string;
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  timestamp: Date;
}

export interface PluginDistribution {
  pluginId: string;
  targetAgents: string[];
  distributionTime: Date;
  autoExecute: boolean;
  priority: number;
}

export interface PluginAuditStatus {
  approved: boolean;
  reviewer: string;
  timestamp: number;
  notes?: string;
  auditHash: string;
}

export enum PluginSecurityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}