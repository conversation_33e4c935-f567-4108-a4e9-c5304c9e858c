#ifndef FILE_MONITOR_PLUGIN_H
#define FILE_MONITOR_PLUGIN_H

#include "plugin_interface.h"
#include "file_watcher.h"
#include "file_analyzer.h"
#include "file_filter.h"

// 文件监控事件类型
typedef enum {
    FILE_EVENT_CREATED = 1,
    FILE_EVENT_MODIFIED = 2,
    FILE_EVENT_DELETED = 3,
    FILE_EVENT_RENAMED = 4,
    FILE_EVENT_MOVED = 5,
    FILE_EVENT_ACCESSED = 6,
    FILE_EVENT_ATTRIBUTE_CHANGED = 7,
    FILE_EVENT_SIZE_CHANGED = 8
} FileEventType;

// 文件监控状态
typedef enum {
    FILE_MONITOR_STATUS_UNINITIALIZED = 0,
    FILE_MONITOR_STATUS_INITIALIZED = 1,
    FILE_MONITOR_STATUS_MONITORING = 2,
    FILE_MONITOR_STATUS_STOPPED = 3,
    FILE_MONITOR_STATUS_ERROR = 4
} FileMonitorStatus;

// 文件监控配置
typedef struct {
    // 监控路径
    char watch_paths[16][MAX_PATH_LENGTH];
    size_t watch_path_count;
    
    // 监控选项
    bool monitor_subdirectories;
    bool monitor_file_creation;
    bool monitor_file_modification;
    bool monitor_file_deletion;
    bool monitor_file_rename;
    bool monitor_file_access;
    bool monitor_attribute_changes;
    
    // 过滤器配置
    char include_patterns[32][128];
    size_t include_pattern_count;
    char exclude_patterns[32][128];
    size_t exclude_pattern_count;
    
    // 性能配置
    uint32_t buffer_size;
    uint32_t max_events_per_batch;
    uint32_t polling_interval_ms;
    
    // 分析配置
    bool enable_file_analysis;
    bool calculate_file_hash;
    bool detect_file_type;
    bool monitor_file_size;
    
    // 报告配置
    bool enable_real_time_reporting;
    uint32_t batch_report_interval_ms;
    
} FileMonitorConfig;

// 文件事件信息
typedef struct {
    FileEventType event_type;
    char file_path[MAX_PATH_LENGTH];
    char old_file_path[MAX_PATH_LENGTH]; // 用于重命名/移动事件
    
    // 文件信息
    uint64_t file_size;
    uint64_t creation_time;
    uint64_t modification_time;
    uint64_t access_time;
    uint32_t file_attributes;
    char file_hash[65]; // SHA256
    char file_type[32];
    
    // 事件信息
    uint64_t event_time;
    uint32_t event_id;
    
    // 扩展信息
    bool is_directory;
    char user_name[64];
    char process_name[128];
    uint32_t process_id;
    
} FileEvent;

// 文件监控统计
typedef struct {
    uint64_t total_events;
    uint64_t events_created;
    uint64_t events_modified;
    uint64_t events_deleted;
    uint64_t events_renamed;
    uint64_t events_accessed;
    uint64_t files_monitored;
    uint64_t directories_monitored;
    uint64_t bytes_processed;
    uint64_t start_time;
    uint64_t last_event_time;
    uint32_t dropped_events;
    float avg_events_per_second;
} FileMonitorStats;

// 文件监控插件上下文
typedef struct {
    FileMonitorConfig config;
    FileMonitorStatus status;
    
    // 监控组件
    FileWatcherContext* watcher;
    FileAnalyzerContext* analyzer;
    FileFilterContext* filter;
    
    // 事件缓冲区
    FileEvent* event_buffer;
    size_t event_buffer_size;
    size_t event_count;
    
    // 统计信息
    FileMonitorStats stats;
    
    // 错误信息
    int last_error_code;
    char last_error_message[256];
    
    // 同步对象
    #ifdef _WIN32
    CRITICAL_SECTION mutex;
    HANDLE event_semaphore;
    HANDLE worker_thread;
    HANDLE stop_event;
    volatile bool should_stop;
    #endif
    
} FileMonitorPluginContext;

// 插件接口实现
int file_monitor_plugin_init(PluginContext* context, ResourceInterface* resources);
int file_monitor_plugin_execute(PluginContext* context, PluginExecutionParams* params, PluginExecutionResult* result);
int file_monitor_plugin_get_data(PluginContext* context, void* buffer, size_t buffer_size, size_t* actual_size);
void file_monitor_plugin_cleanup(PluginContext* context);
int file_monitor_plugin_get_metadata(PluginMetadata* metadata);
int file_monitor_plugin_configure(PluginContext* context, const char* config_json);

// 监控控制
int file_monitor_start_monitoring(FileMonitorPluginContext* ctx);
int file_monitor_stop_monitoring(FileMonitorPluginContext* ctx);
int file_monitor_pause_monitoring(FileMonitorPluginContext* ctx);
int file_monitor_resume_monitoring(FileMonitorPluginContext* ctx);

// 路径管理
int file_monitor_add_watch_path(FileMonitorPluginContext* ctx, const char* path);
int file_monitor_remove_watch_path(FileMonitorPluginContext* ctx, const char* path);
int file_monitor_clear_watch_paths(FileMonitorPluginContext* ctx);

// 事件处理
int file_monitor_get_events(FileMonitorPluginContext* ctx, FileEvent* events, size_t max_events, size_t* actual_count);
int file_monitor_flush_events(FileMonitorPluginContext* ctx);
void file_monitor_clear_events(FileMonitorPluginContext* ctx);

// 过滤器管理
int file_monitor_add_include_pattern(FileMonitorPluginContext* ctx, const char* pattern);
int file_monitor_add_exclude_pattern(FileMonitorPluginContext* ctx, const char* pattern);
int file_monitor_clear_filters(FileMonitorPluginContext* ctx);

// 统计信息
void file_monitor_get_statistics(FileMonitorPluginContext* ctx, FileMonitorStats* stats);
void file_monitor_reset_statistics(FileMonitorPluginContext* ctx);

// 工具函数
const char* file_event_type_to_string(FileEventType event_type);
const char* file_monitor_status_to_string(FileMonitorStatus status);
int file_monitor_validate_config(const FileMonitorConfig* config);
int file_monitor_is_path_monitored(FileMonitorPluginContext* ctx, const char* path);

// 事件序列化
int file_monitor_serialize_events(const FileEvent* events, size_t event_count, char* json_buffer, size_t buffer_size);
int file_monitor_serialize_event(const FileEvent* event, char* json_buffer, size_t buffer_size);

// 配置管理
int file_monitor_load_config_from_json(const char* json, FileMonitorConfig* config);
int file_monitor_save_config_to_json(const FileMonitorConfig* config, char* json_buffer, size_t buffer_size);

// 文件系统工具
int file_monitor_get_file_info(const char* file_path, FileEvent* event);
bool file_monitor_file_exists(const char* file_path);
bool file_monitor_is_directory(const char* file_path);
int file_monitor_calculate_file_hash(const char* file_path, char* hash_buffer, size_t buffer_size);

#ifdef _WIN32
// Windows特定功能
DWORD WINAPI file_monitor_worker_thread(LPVOID param);
int file_monitor_setup_windows_watcher(FileMonitorPluginContext* ctx);
int file_monitor_process_windows_events(FileMonitorPluginContext* ctx);
#endif

#endif // FILE_MONITOR_PLUGIN_H