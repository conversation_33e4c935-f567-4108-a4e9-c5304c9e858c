import fs from 'fs-extra';
import path from 'path';

/**
 * 文件操作辅助函数
 */
export class FileHelper {
  /**
   * 确保目录存在
   */
  public static async ensureDir(dirPath: string): Promise<void> {
    await fs.ensureDir(dirPath);
  }
  
  /**
   * 安全读取文件
   */
  public static async readFile(filePath: string): Promise<Buffer | null> {
    try {
      return await fs.readFile(filePath);
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 安全写入文件
   */
  public static async writeFile(filePath: string, data: Buffer | string): Promise<boolean> {
    try {
      await fs.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, data);
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * 获取文件大小
   */
  public static async getFileSize(filePath: string): Promise<number | null> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 检查文件是否存在
   */
  public static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * 删除文件
   */
  public static async deleteFile(filePath: string): Promise<boolean> {
    try {
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }
}

/**
 * 时间操作辅助函数
 */
export class TimeHelper {
  /**
   * 格式化时间戳
   */
  public static formatTimestamp(timestamp: number): string {
    return new Date(timestamp).toISOString();
  }
  
  /**
   * 获取当前时间戳
   */
  public static now(): number {
    return Date.now();
  }
  
  /**
   * 计算时间差（毫秒）
   */
  public static timeDiff(start: number, end?: number): number {
    return (end || Date.now()) - start;
  }
  
  /**
   * 检查时间戳是否过期
   */
  public static isExpired(timestamp: number, maxAge: number): boolean {
    return Date.now() - timestamp > maxAge;
  }
  
  /**
   * 人性化时间显示
   */
  public static humanizeTime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}天${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }
}

/**
 * 数据验证辅助函数
 */
export class ValidationHelper {
  /**
   * 验证Agent ID格式
   */
  public static isValidAgentId(agentId: string): boolean {
    return /^[a-zA-Z0-9_-]{8,32}$/.test(agentId);
  }
  
  /**
   * 验证插件ID格式
   */
  public static isValidPluginId(pluginId: string): boolean {
    return /^[a-zA-Z0-9_.-]{4,64}$/.test(pluginId);
  }
  
  /**
   * 验证版本号格式
   */
  public static isValidVersion(version: string): boolean {
    return /^\d+\.\d+\.\d+$/.test(version);
  }
  
  /**
   * 验证文件大小
   */
  public static isValidFileSize(size: number, maxSize: number = 10 * 1024 * 1024): boolean {
    return size > 0 && size <= maxSize;
  }
  
  /**
   * 验证IP地址
   */
  public static isValidIP(ip: string): boolean {
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }
  
  /**
   * 清理字符串（防XSS）
   */
  public static sanitizeString(input: string): string {
    return input
      .replace(/[<>\"']/g, '')
      .trim()
      .substring(0, 1000);
  }
}

/**
 * 性能监控辅助函数
 */
export class PerformanceHelper {
  private static timers: Map<string, number> = new Map();
  
  /**
   * 开始计时
   */
  public static startTimer(name: string): void {
    PerformanceHelper.timers.set(name, Date.now());
  }
  
  /**
   * 结束计时并返回耗时
   */
  public static endTimer(name: string): number {
    const startTime = PerformanceHelper.timers.get(name);
    if (!startTime) {
      return 0;
    }
    
    const duration = Date.now() - startTime;
    PerformanceHelper.timers.delete(name);
    return duration;
  }
  
  /**
   * 内存使用情况
   */
  public static getMemoryUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  }
  
  /**
   * 格式化内存使用
   */
  public static formatMemoryUsage(bytes: number): string {
    const mb = bytes / 1024 / 1024;
    return `${Math.round(mb * 100) / 100} MB`;
  }
}

/**
 * 数据处理辅助函数
 */
export class DataHelper {
  /**
   * 深度复制对象
   */
  public static deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }
  
  /**
   * 安全的JSON解析
   */
  public static safeJsonParse<T>(jsonString: string, defaultValue: T): T {
    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      return defaultValue;
    }
  }
  
  /**
   * 将对象转换为查询字符串
   */
  public static objectToQueryString(obj: Record<string, any>): string {
    return Object.keys(obj)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
      .join('&');
  }
  
  /**
   * 分页数据
   */
  public static paginate<T>(
    data: T[], 
    page: number = 1, 
    pageSize: number = 10
  ): { data: T[]; total: number; page: number; pageSize: number; totalPages: number } {
    const offset = (page - 1) * pageSize;
    const paginatedData = data.slice(offset, offset + pageSize);
    
    return {
      data: paginatedData,
      total: data.length,
      page,
      pageSize,
      totalPages: Math.ceil(data.length / pageSize)
    };
  }
  
  /**
   * 数组去重
   */
  public static uniqueArray<T>(array: T[]): T[] {
    return [...new Set(array)];
  }
}