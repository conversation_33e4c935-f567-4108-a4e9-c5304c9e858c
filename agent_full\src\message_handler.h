#ifndef MESSAGE_HANDLER_H
#define MESSAGE_HANDLER_H

#include "agent.h"

// Message handling functions
AgentResult message_handler_init(AgentContext* ctx);
void message_handler_cleanup(AgentContext* ctx);
AgentResult message_handler_process(AgentContext* ctx, const char* message);
AgentResult message_handler_send_status(AgentContext* ctx);
AgentResult message_handler_handle_command(AgentContext* ctx, const char* command, const char* params);
AgentResult message_handler_handle_plugin_download(AgentContext* ctx, const char* message);
AgentResult message_handler_handle_plugin_execute(AgentContext* ctx, const char* message);
AgentResult message_handler_handle_plugin_command(AgentContext* ctx, const char* message);

// Message types
typedef enum {
    MSG_TYPE_HEARTBEAT,
    MSG_TYPE_SYSTEM_INFO,
    MSG_TYPE_PLUGIN_DATA,
    MSG_TYPE_STATUS,
    MSG_TYPE_COMMAND,
    MSG_TYPE_PLUGIN_DOWNLOAD,
    MSG_TYPE_PLUGIN_EXECUTE,
    MSG_TYPE_PLUGIN_COMMAND,
    MSG_TYPE_ERROR,
    MSG_TYPE_UNKNOWN
} MessageType;

// Parse message type from JSON
MessageType parse_message_type(const char* message);
char* extract_json_field(const char* json, const char* field);

#endif // MESSAGE_HANDLER_H