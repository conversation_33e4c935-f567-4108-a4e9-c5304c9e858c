import { Router } from 'express';
import multer from 'multer';
import { PluginController } from '../controllers/PluginController';

const router = Router();
const pluginController = new PluginController();

// 配置文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件扩展名
    const allowedExtensions = ['.dll', '.so', '.dylib'];
    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    
    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only .dll, .so, and .dylib files are allowed.'));
    }
  }
});

// 插件管理路由
router.post('/upload', upload.single('plugin'), pluginController.uploadPlugin);
router.get('/', pluginController.getAllPlugins);
router.get('/:id', pluginController.getPlugin);
router.delete('/:id', pluginController.deletePlugin);

// 插件分发路由
router.post('/:pluginId/distribute', pluginController.distributeToAgent);
router.post('/:pluginId/distribute/batch', pluginController.distributeToBatch);
router.post('/:pluginId/execute', pluginController.executePlugin);

// 插件历史和统计路由
router.get('/:pluginId/distributions', pluginController.getDistributionHistory);
router.get('/:pluginId/executions', pluginController.getExecutionResults);

// 插件安全路由
router.post('/:id/audit', pluginController.auditPlugin);
router.get('/:id/security', pluginController.getPluginSecurity);
router.get('/:id/verify-signature', pluginController.verifyPluginSignature);

// 文件管理命令路由
router.post('/filemanager/:agentId/command', pluginController.sendFileManagerCommand);

export default router;