^C:\USERS\<USER>\DESKTOP\WIN\TEST_PLUGIN\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/win/test_plugin -BC:/Users/<USER>/Desktop/win/test_plugin/build --check-stamp-file C:/Users/<USER>/Desktop/win/test_plugin/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
