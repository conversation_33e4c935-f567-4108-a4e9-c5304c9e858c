
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.12.12+1cce77968
      鐢熸垚鍚姩鏃堕棿涓?2025/8/3 19:41:39銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\lib" /LIBPATH:"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
        鎴栨壒澶勭悊鏂囦欢銆?
        鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
        "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        -File 褰㈠紡鍙傛暟鐨勫疄闄呭弬鏁扳€淐:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1鈥濅笉瀛樺湪銆傝鎻愪緵鐜版湁鈥?ps1鈥濇枃浠剁殑璺緞锛屼綔涓?-File 褰㈠紡鍙傛暟鐨勪竴涓疄闄呭弬鏁般€?
        Windows PowerShell
        鐗堟潈鎵€鏈?(C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
        
        灏濊瘯鏂扮殑璺ㄥ钩鍙?PowerShell https://aka.ms/pscore6
        
        鍛戒护鈥?C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?-196608銆?
      C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\vcpkg.targets(247,5): warning : [vcpkg] Failed to gather app local DLL dependencies, program may not run. Set VcpkgApplocalDeps to false in your project file to suppress this warning. PowerShell arguments: -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log" [C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj]
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
      
      鈥淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣) (1) ->
      (AppLocalFromInstalled 鐩爣) -> 
        C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\vcpkg.targets(247,5): warning : [vcpkg] Failed to gather app local DLL dependencies, program may not run. Set VcpkgApplocalDeps to false in your project file to suppress this warning. PowerShell arguments: -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log" [C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj]
      
          1 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:03.98
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/Desktop/win/agent_full/build/CMakeFiles/3.31.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/win/agent_full/build/CMakeFiles/CMakeScratch/TryCompile-fc428y"
      binary: "C:/Users/<USER>/Desktop/win/agent_full/build/CMakeFiles/CMakeScratch/TryCompile-fc428y"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/win/agent_full/build/CMakeFiles/CMakeScratch/TryCompile-fc428y'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_5b7cc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.12.12+1cce77968
        鐢熸垚鍚姩鏃堕棿涓?2025/8/3 19:41:43銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\cmTC_5b7cc.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_5b7cc.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5b7cc.dir\\Debug\\\\" /Fd"cmTC_5b7cc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.42.34436 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5b7cc.dir\\Debug\\\\" /Fd"cmTC_5b7cc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" /INCREMENTAL /ILK:"cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.ilk" /NOLOGO /LIBPATH:"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/win/agent_full/build/CMakeFiles/CMakeScratch/TryCompile-fc428y/Debug/cmTC_5b7cc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/win/agent_full/build/CMakeFiles/CMakeScratch/TryCompile-fc428y/Debug/cmTC_5b7cc.lib" /MACHINE:X64  /machine:x64 cmTC_5b7cc.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_5b7cc.vcxproj -> C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\bin" "cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.write.1u.tlog" "cmTC_5b7cc.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
          鎴栨壒澶勭悊鏂囦欢銆?
          鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\bin" "cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.write.1u.tlog" "cmTC_5b7cc.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
          "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\bin" "cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.write.1u.tlog" "cmTC_5b7cc.dir\\Debug\\vcpkg.applocal.log"
          -File 褰㈠紡鍙傛暟鐨勫疄闄呭弬鏁扳€淐:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1鈥濅笉瀛樺湪銆傝鎻愪緵鐜版湁鈥?ps1鈥濇枃浠剁殑璺緞锛屼綔涓?-File 褰㈠紡鍙傛暟鐨勪竴涓疄闄呭弬鏁般€?
          Windows PowerShell
          鐗堟潈鎵€鏈?(C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          
          灏濊瘯鏂扮殑璺ㄥ钩鍙?PowerShell https://aka.ms/pscore6
          
          鍛戒护鈥?C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\bin" "cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.write.1u.tlog" "cmTC_5b7cc.dir\\Debug\\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?-196608銆?
        C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\vcpkg.targets(247,5): warning : [vcpkg] Failed to gather app local DLL dependencies, program may not run. Set VcpkgApplocalDeps to false in your project file to suppress this warning. PowerShell arguments: -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\bin" "cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.write.1u.tlog" "cmTC_5b7cc.dir\\Debug\\vcpkg.applocal.log" [C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\cmTC_5b7cc.vcxproj]
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\cmTC_5b7cc.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
        
        鈥淐:\\Users\\Administrator\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\cmTC_5b7cc.vcxproj鈥?榛樿鐩爣) (1) ->
        (AppLocalFromInstalled 鐩爣) -> 
          C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\vcpkg.targets(247,5): warning : [vcpkg] Failed to gather app local DLL dependencies, program may not run. Set VcpkgApplocalDeps to false in your project file to suppress this warning. PowerShell arguments: -ExecutionPolicy Bypass -noprofile -File "C:\\vcpkg-2025.01.13\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\Debug\\cmTC_5b7cc.exe" "C:\\vcpkg-2025.01.13\\installed\\x64-windows\\debug\\bin" "cmTC_5b7cc.dir\\Debug\\cmTC_5b7cc.tlog\\cmTC_5b7cc.write.1u.tlog" "cmTC_5b7cc.dir\\Debug\\vcpkg.applocal.log" [C:\\Users\\<USER>\\Desktop\\win\\agent_full\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fc428y\\cmTC_5b7cc.vcxproj]
        
            1 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.17
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.42.34436.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
