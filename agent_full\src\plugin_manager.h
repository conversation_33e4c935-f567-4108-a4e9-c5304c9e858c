#ifndef PLUGIN_MANAGER_H
#define PLUGIN_MANAGER_H

#include "agent.h"

// Plugin manager functions
AgentResult plugin_manager_init(AgentContext* ctx);
void plugin_manager_cleanup(AgentContext* ctx);
AgentResult plugin_manager_scan_directory(AgentContext* ctx);
AgentResult plugin_manager_load_plugin(AgentContext* ctx, const char* plugin_path);
AgentResult plugin_manager_unload_plugin(AgentContext* ctx, const char* plugin_name);
AgentResult plugin_manager_execute_plugins(AgentContext* ctx);
PluginInfo* plugin_manager_find_plugin(AgentContext* ctx, const char* plugin_name);
void plugin_manager_get_status(AgentContext* ctx, char* buffer, size_t buffer_size);

// Plugin execution
AgentResult plugin_execute_single(PluginInfo* plugin);
char* plugin_collect_data(AgentContext* ctx);

// Remote plugin management
AgentResult plugin_manager_download_plugin(AgentContext* ctx, const char* plugin_id, 
                                          const char* plugin_name, const char* plugin_data, 
                                          const char* checksum, int auto_execute);
AgentResult plugin_manager_execute_plugin_by_id(AgentContext* ctx, const char* plugin_id);

#endif // PLUGIN_MANAGER_H