﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{6396A678-95FA-3530-A2A6-C13305B711DC}"
	ProjectSection(ProjectDependencies) = postProject
		{030398B8-C015-352E-8EC1-854EB55CBED9} = {030398B8-C015-352E-8EC1-854EB55CBED9}
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC} = {75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{0E7DDEBD-9667-3FB0-97FD-6C8B4B1D21F4}"
	ProjectSection(ProjectDependencies) = postProject
		{6396A678-95FA-3530-A2A6-C13305B711DC} = {6396A678-95FA-3530-A2A6-C13305B711DC}
		{030398B8-C015-352E-8EC1-854EB55CBED9} = {030398B8-C015-352E-8EC1-854EB55CBED9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{030398B8-C015-352E-8EC1-854EB55CBED9}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_plugin", "test_plugin.vcxproj", "{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}"
	ProjectSection(ProjectDependencies) = postProject
		{030398B8-C015-352E-8EC1-854EB55CBED9} = {030398B8-C015-352E-8EC1-854EB55CBED9}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6396A678-95FA-3530-A2A6-C13305B711DC}.Debug|x64.ActiveCfg = Debug|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.Debug|x64.Build.0 = Debug|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.Release|x64.ActiveCfg = Release|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.Release|x64.Build.0 = Release|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6396A678-95FA-3530-A2A6-C13305B711DC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{0E7DDEBD-9667-3FB0-97FD-6C8B4B1D21F4}.Debug|x64.ActiveCfg = Debug|x64
		{0E7DDEBD-9667-3FB0-97FD-6C8B4B1D21F4}.Release|x64.ActiveCfg = Release|x64
		{0E7DDEBD-9667-3FB0-97FD-6C8B4B1D21F4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0E7DDEBD-9667-3FB0-97FD-6C8B4B1D21F4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.Debug|x64.ActiveCfg = Debug|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.Debug|x64.Build.0 = Debug|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.Release|x64.ActiveCfg = Release|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.Release|x64.Build.0 = Release|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{030398B8-C015-352E-8EC1-854EB55CBED9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.Debug|x64.ActiveCfg = Debug|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.Debug|x64.Build.0 = Debug|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.Release|x64.ActiveCfg = Release|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.Release|x64.Build.0 = Release|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{75853E6F-68C0-3B9C-AB8C-BA94C9370DAC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {652EAC5C-5367-3DCA-B58A-D3F2DFC80CDA}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
