cmake_minimum_required(VERSION 3.16)
project(TestPlugin VERSION 1.0.0 LANGUAGES C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler options
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra)
endif()

# Create shared library (DLL on Windows)
add_library(test_plugin SHARED test_plugin.c)

# Set properties
set_target_properties(test_plugin PROPERTIES
    PREFIX ""
    OUTPUT_NAME "test_plugin"
    SUFFIX ".dll"
)

# Install
install(TARGETS test_plugin
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)