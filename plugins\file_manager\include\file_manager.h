#ifndef FILE_MANAGER_H
#define FILE_MANAGER_H

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <direct.h>
#include <shlobj.h>

// 文件操作命令类型
typedef enum {
    FM_CMD_LIST_DIRECTORY = 1,    // 列出目录内容
    FM_CMD_GET_FILE_INFO = 2,     // 获取文件信息
    FM_CMD_DOWNLOAD_FILE = 3,     // 下载文件到服务器
    FM_CMD_UPLOAD_FILE = 4,       // 从服务器上传文件
    FM_CMD_DELETE_FILE = 5,       // 删除文件
    FM_CMD_CREATE_DIRECTORY = 6,  // 创建目录
    FM_CMD_MOVE_FILE = 7,         // 移动/重命名文件
    FM_CMD_COPY_FILE = 8,         // 复制文件
    FM_CMD_GET_DRIVES = 9,        // 获取磁盘驱动器列表
    FM_CMD_SEARCH_FILES = 10      // 搜索文件
} FileManagerCommand;

// 文件信息结构
typedef struct {
    char name[260];
    char full_path[520];
    long long size;
    time_t created;
    time_t modified;
    time_t accessed;
    int is_directory;
    int is_hidden;
    int is_system;
    char attributes[32];
} FileInfo;

// 磁盘驱动器信息
typedef struct {
    char drive_letter[4];
    char drive_type[32];
    long long total_size;
    long long free_size;
    char volume_name[256];
} DriveInfo;

// 文件管理上下文
typedef struct {
    char current_directory[520];
    char last_operation[1024];
    int operation_count;
    time_t last_access;
    int max_file_size_mb;  // 最大文件大小限制（MB）
} FileManagerContext;

// 插件命令处理函数
char* fm_handle_command(const char* command_json);

// 核心文件操作函数
char* fm_list_directory(const char* path);
char* fm_get_file_info(const char* file_path);
char* fm_download_file(const char* file_path);
int fm_upload_file(const char* file_path, const char* base64_data);
int fm_delete_file(const char* file_path);
int fm_create_directory(const char* dir_path);
int fm_move_file(const char* source_path, const char* dest_path);
int fm_copy_file(const char* source_path, const char* dest_path);
char* fm_get_drives(void);
char* fm_search_files(const char* search_path, const char* pattern);

// 工具函数
int fm_init_context(void);
void fm_cleanup_context(void);
char* fm_create_json_response(const char* command, int success, const char* data, const char* error);
char* fm_encode_base64(const unsigned char* data, size_t data_len);
unsigned char* fm_decode_base64(const char* encoded_data, size_t* output_len);
int fm_is_safe_path(const char* path);
char* fm_normalize_path(const char* path);

#endif // FILE_MANAGER_H