#include <stdio.h>
#include <windows.h>

int main() {
    printf("直接测试System32目录访问（绕过Agent消息系统）\n");
    printf("================================================\n\n");
    
    // 加载插件DLL
    HMODULE plugin_handle = LoadLibraryA(".\\plugins\\file_manager\\build\\Release\\file_manager.dll");
    if (!plugin_handle) {
        printf("❌ 无法加载插件DLL，错误: %lu\n", GetLastError());
        return 1;
    }
    
    // 获取插件函数
    typedef int (*plugin_init_func)(void);
    typedef char* (*plugin_handle_command_func)(const char*);
    
    plugin_init_func plugin_init = (plugin_init_func)GetProcAddress(plugin_handle, "plugin_init");
    plugin_handle_command_func plugin_handle_command = (plugin_handle_command_func)GetProcAddress(plugin_handle, "plugin_handle_command");
    
    if (!plugin_init || !plugin_handle_command) {
        printf("❌ 无法获取插件函数\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    
    // 初始化插件
    if (plugin_init() != 0) {
        printf("❌ 插件初始化失败\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    printf("✅ 插件初始化成功\n\n");
    
    // 测试System32目录
    printf("测试1: 访问 C:\\\\Windows\\\\System32\n");
    const char* cmd1 = "{\"command\":\"list_directory\",\"path\":\"C:\\\\Windows\\\\System32\"}";
    char* result1 = plugin_handle_command(cmd1);
    if (result1) {
        printf("✅ System32访问成功！前200字符: %.200s...\n\n", result1);
        free(result1);
    } else {
        printf("❌ System32访问失败\n\n");
    }
    
    // 测试普通Windows目录
    printf("测试2: 访问 C:\\\\Windows\n");
    const char* cmd2 = "{\"command\":\"list_directory\",\"path\":\"C:\\\\Windows\"}";
    char* result2 = plugin_handle_command(cmd2);
    if (result2) {
        printf("✅ Windows目录访问成功！前200字符: %.200s...\n\n", result2);
        free(result2);
    } else {
        printf("❌ Windows目录访问失败\n\n");
    }
    
    // 测试C盘根目录
    printf("测试3: 访问 C:\\\\\n");
    const char* cmd3 = "{\"command\":\"list_directory\",\"path\":\"C:\\\\\"}";
    char* result3 = plugin_handle_command(cmd3);
    if (result3) {
        printf("✅ C盘根目录访问成功！前200字符: %.200s...\n\n", result3);
        free(result3);
    } else {
        printf("❌ C盘根目录访问失败\n\n");
    }
    
    FreeLibrary(plugin_handle);
    printf("测试完成 - 如果System32成功，说明安全限制已完全移除！\n");
    return 0;
}