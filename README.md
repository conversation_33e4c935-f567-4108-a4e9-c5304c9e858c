# 分布式监控系统

## 项目概述

这是一个基于插件容器架构的分布式监控系统，支持远程DLL插件动态加载和执行。系统采用C语言开发轻量级Agent客户端，Node.js + TypeScript开发管理服务器，通过WebSocket实现双向实时通信，使用XOR加密确保数据传输安全。

## 核心特性

- **插件容器架构**: Agent作为轻量级运行时容器，所有业务逻辑通过DLL插件实现
- **内存DLL加载**: 支持DLL插件在内存中直接加载执行，无需写入磁盘
- **实时双向通信**: 基于WebSocket的加密通信，支持100个并发连接
- **动态插件管理**: 支持插件的远程下发、加载、卸载和更新
- **摄像头监控**: 内置摄像头监控插件，支持最小画质实时传输
- **跨平台兼容**: 支持Windows 7及以上系统
- **Web管理控制台**: 现代化的Web界面，实时监控和管理所有Agent

## 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                    分布式监控系统架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐                    ┌─────────────────┐     │
│  │   Node.js       │                    │   C Language    │     │
│  │   服务器端       │◄──── WebSocket ────┤   Agent端       │     │
│  │                 │      (XOR加密)      │                 │     │
│  │  ┌─────────────┐│                    │  ┌─────────────┐│     │
│  │  │连接管理层   ││                    │  │通信层       ││     │
│  │  └─────────────┘│                    │  └─────────────┘│     │
│  │  ┌─────────────┐│                    │  ┌─────────────┐│     │
│  │  │插件分发层   ││                    │  │插件管理层   ││     │
│  │  └─────────────┘│                    │  └─────────────┘│     │
│  │  ┌─────────────┐│                    │  ┌─────────────┐│     │
│  │  │控制逻辑层   ││                    │  │资源抽象层   ││     │
│  │  └─────────────┘│                    │  └─────────────┘│     │
│  └─────────────────┘                    └─────────────────┘     │
│                                                                 │
│                        ┌─────────────────┐                     │
│                        │   DLL插件生态   │                     │
│                        │ ┌─────────────┐ │                     │
│                        │ │摄像头监控   │ │                     │
│                        │ │文件监控     │ │                     │
│                        │ │系统信息     │ │                     │
│                        │ └─────────────┘ │                     │
│                        └─────────────────┘                     │
└─────────────────────────────────────────────────────────────────┘
```

### 技术栈

**服务器端:**
- Node.js + TypeScript
- WebSocket (ws库)
- Express.js
- React + TypeScript (Web控制台)

**Agent端:**
- C语言
- WebSocket客户端
- DirectShow API (摄像头)
- Windows PE加载器

**通信协议:**
- WebSocket双向通信
- XOR加密算法
- JSON消息格式

## 项目结构

```
distributed-monitoring-system/
├── docs/                               # 文档目录
│   ├── architecture-design.md          # 架构设计文档
│   ├── project-structure.md            # 项目结构说明
│   └── development-roadmap.md          # 开发路线图
├── server/                             # 服务器端代码 (Node.js + TypeScript)
│   ├── src/                            # TypeScript源代码
│   ├── package.json                    # 依赖配置
│   └── tsconfig.json                   # TypeScript配置
├── agent_full/                         # Agent端代码 (C语言)
│   ├── src/                            # C语言源代码
│   ├── plugins/                        # 测试插件目录
│   └── CMakeLists.txt                  # CMake构建配置
├── plugins/                            # 插件开发目录
│   ├── camera_monitor/                 # 摄像头监控插件
│   ├── file_monitor/                   # 文件监控插件
│   ├── system_info/                    # 系统信息插件
│   └── plugin_template/                # 插件开发模板
├── config/                             # 配置文件目录
│   ├── agent.json                      # Agent配置文件
│   └── server.json                     # 服务器配置文件
├── scripts/                            # 构建和部署脚本
├── build.cmd                           # Windows构建脚本
├── start_server.cmd                    # 服务器启动脚本
└── start_agent.cmd                     # Agent启动脚本
```

## 核心功能

### 1. 插件容器架构
- Agent作为轻量级运行时容器
- 支持多种类型插件的标准化接入
- 插件间隔离和资源管理
- 动态插件生命周期管理

### 2. 内存DLL加载
- PE文件格式解析
- 内存映射和重定位处理
- 符号解析和导入表处理
- 沙箱化执行环境

### 3. 实时通信
- WebSocket双向通信
- XOR加密数据传输
- 心跳保持和断线重连
- 消息路由和处理

### 4. 摄像头监控
- DirectShow API集成
- 最小画质优化传输
- 实时图像采集和压缩
- 多摄像头设备支持

### 5. Web管理控制台
- Agent连接状态监控
- 插件管理和分发
- 实时数据展示
- 系统配置和日志查看

## 开发路线图

项目采用7个阶段的迭代开发模式：

1. **基础框架搭建** (2-3周) - 项目结构和开发环境
2. **通信协议实现** (2-3周) - WebSocket和加密通信
3. **插件系统开发** (3-4周) - DLL加载和插件管理
4. **核心功能实现** (2-3周) - 摄像头监控和资源抽象
5. **Web控制台开发** (2-3周) - 管理界面和实时通信
6. **测试与优化** (2-3周) - 功能测试和性能优化
7. **部署与文档** (1-2周) - 部署自动化和文档完善

总开发周期：**14-21周**

## 技术亮点

### 插件化设计
- 业务逻辑完全解耦，通过插件实现
- 支持插件热更新和版本管理
- 标准化插件接口，便于第三方开发

### 高性能架构
- 支持100个并发Agent连接
- 异步I/O和事件驱动架构
- 内存优化和资源管理

### 安全机制
- XOR加密通信
- 插件沙箱执行
- 权限控制和访问限制

### 跨平台兼容
- 支持Windows 7及以上系统
- 兼容不同版本的DirectShow API
- 标准化的构建和部署流程

## 快速开始

### 环境要求
- **服务器端**: Node.js 16+, TypeScript 4+
- **Agent端**: Visual Studio 2019+, CMake 3.16+, Windows SDK
- **开发工具**: Git, VS Code (推荐)

### 构建步骤

#### 方式一：使用构建脚本（推荐）
```cmd
# 运行自动构建脚本
build.cmd
```

#### 方式二：手动构建
```bash
# 构建服务器端
cd server
npm install
npm run build

# 构建Agent端
cd ../agent_full
mkdir build && cd build
cmake ..
cmake --build . --config Release
```

### 运行系统

#### 方式一：使用启动脚本（推荐）
```cmd
# 启动服务器（在一个终端中）
start_server.cmd

# 启动Agent（在另一个终端中）
start_agent.cmd
```

#### 方式二：手动启动
```bash
# 启动服务器
cd server
npm start

# 运行Agent
cd agent_full/build/Release
agent.exe
```

### 验证运行状态
- 服务器启动后会显示：`HTTP服务器运行在端口: 3000` 和 `WebSocket服务器运行在端口: 8080`
- Agent连接后会显示：`Connected to WebSocket server: localhost:8080`
- 系统会自动扫描并加载插件目录中的DLL文件

## 文档

- [架构设计文档](docs/architecture-design.md) - 详细的系统架构设计
- [项目结构说明](docs/project-structure.md) - 完整的项目组织结构
- [开发路线图](docs/development-roadmap.md) - 详细的开发计划和里程碑
- [插件开发指南](docs/plugin-development-guide.md) - 插件开发规范和示例
- [API接口规范](docs/api-specification.md) - 完整的API文档
- [部署指南](docs/deployment-guide.md) - 生产环境部署说明

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。请确保：

1. 遵循项目的代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 通过所有CI检查

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者
- 参与项目讨论区

---

**注意**: 这是一个技术演示项目，请确保在合法合规的环境中使用，并遵守相关法律法规。
