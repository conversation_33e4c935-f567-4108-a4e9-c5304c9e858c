# 分布式监控系统 API 文档

## 概述

分布式监控系统提供了完整的REST API，用于管理插件、Agent连接和系统监控。所有API都基于HTTP协议，使用JSON格式进行数据交换。

## 基础信息

- **基础URL**: `http://localhost:3000/api`
- **数据格式**: JSON
- **认证方式**: 无（开发环境）
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {}, // 具体数据
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "error": "错误描述",
  "details": "详细错误信息（可选）"
}
```

## 插件管理 API

### 1. 上传插件

**POST** `/plugins/upload`

上传一个新的插件文件到服务器。

#### 请求参数

- **Content-Type**: `multipart/form-data`

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| plugin | File | 是 | 插件文件（.dll, .so, .dylib） |
| name | String | 是 | 插件名称 |
| version | String | 是 | 插件版本 |
| description | String | 否 | 插件描述 |
| author | String | 否 | 插件作者 |
| category | String | 是 | 插件类别（camera/file_monitor/system_info/network/custom） |
| targetSystems | JSON Array | 否 | 目标系统 ["windows", "linux", "macos"] |
| dependencies | JSON Array | 否 | 依赖列表 [] |

#### 示例请求
```bash
curl -X POST \
  -F "plugin=@test_plugin.dll" \
  -F "name=test_plugin" \
  -F "version=1.0.0" \
  -F "description=测试插件" \
  -F "author=开发团队" \
  -F "category=custom" \
  -F "targetSystems=[\"windows\"]" \
  -F "dependencies=[]" \
  http://localhost:3000/api/plugins/upload
```

#### 响应示例
```json
{
  "success": true,
  "plugin": {
    "id": "00998357-8355-46d1-93b1-acd45ef0f918",
    "name": "test_plugin",
    "version": "1.0.0",
    "description": "测试插件",
    "author": "开发团队",
    "filePath": "uploads/plugins/00998357-8355-46d1-93b1-acd45ef0f918_test_plugin.dll",
    "fileSize": 10752,
    "checksum": "d0ca6bd416fc5db7db58a28d6bd828ae9f023e92aaa6e42f4f3e64d3f64f5617",
    "signature": "37533d30f87497617a9ac4559d5fa1923c8fc885bd1eb5ab8632774a262d858a",
    "uploadTime": "2025-08-03T12:24:02.642Z",
    "dependencies": [],
    "targetSystems": ["windows"],
    "category": "custom",
    "enabled": true
  }
}
```

### 2. 获取所有插件

**GET** `/plugins`

获取系统中所有插件的列表。

#### 响应示例
```json
{
  "success": true,
  "plugins": [
    {
      "id": "00998357-8355-46d1-93b1-acd45ef0f918",
      "name": "test_plugin",
      "version": "1.0.0",
      "description": "测试插件",
      "author": "开发团队",
      "enabled": true,
      "uploadTime": "2025-08-03T12:24:02.642Z"
    }
  ],
  "count": 1
}
```

### 3. 获取指定插件信息

**GET** `/plugins/{pluginId}`

获取指定插件的详细信息。

#### 路径参数
- `pluginId`: 插件ID

#### 响应示例
```json
{
  "success": true,
  "plugin": {
    "id": "00998357-8355-46d1-93b1-acd45ef0f918",
    "name": "test_plugin",
    "version": "1.0.0",
    "description": "测试插件",
    "author": "开发团队",
    "fileSize": 10752,
    "checksum": "d0ca6bd416fc5db7db58a28d6bd828ae9f023e92aaa6e42f4f3e64d3f64f5617",
    "enabled": true,
    "uploadTime": "2025-08-03T12:24:02.642Z"
  }
}
```

### 4. 删除插件

**DELETE** `/plugins/{pluginId}`

删除指定的插件。

#### 路径参数
- `pluginId`: 插件ID

#### 响应示例
```json
{
  "success": true,
  "message": "Plugin deleted successfully"
}
```

## 插件分发 API

### 5. 分发插件到单个Agent

**POST** `/plugins/{pluginId}/distribute`

将插件分发到指定的Agent。

#### 路径参数
- `pluginId`: 插件ID

#### 请求体
```json
{
  "agentId": "efcbd189-4aca-4240-a81a-9ce7ff157de2",
  "autoExecute": true,
  "priority": 1
}
```

#### 请求参数说明
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| agentId | String | 是 | 目标Agent ID |
| autoExecute | Boolean | 否 | 是否自动执行（默认false） |
| priority | Number | 否 | 优先级（默认1） |

#### 响应示例
```json
{
  "success": true,
  "message": "Plugin distributed successfully",
  "distributionId": "47ceb396-99ba-425e-9bfe-7545079685d5"
}
```

### 6. 批量分发插件

**POST** `/plugins/{pluginId}/distribute/batch`

将插件批量分发到多个Agent。

#### 路径参数
- `pluginId`: 插件ID

#### 请求体
```json
{
  "agentIds": [
    "efcbd189-4aca-4240-a81a-9ce7ff157de2",
    "another-agent-id"
  ],
  "autoExecute": false,
  "priority": 2
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "Plugin distributed to 2/2 agents",
  "results": [
    {
      "success": true,
      "agentId": "efcbd189-4aca-4240-a81a-9ce7ff157de2",
      "distributionId": "a53ec4f2-99ec-4990-ad68-6f3ec6197633"
    },
    {
      "success": true,
      "agentId": "another-agent-id",
      "distributionId": "b64fd5g3-10fd-5091-be69-7g4fd7298d44"
    }
  ],
  "summary": {
    "total": 2,
    "success": 2,
    "failed": 0
  }
}
```

### 7. 远程执行插件

**POST** `/plugins/{pluginId}/execute`

远程执行Agent上的指定插件。

#### 路径参数
- `pluginId`: 插件ID

#### 请求体
```json
{
  "agentId": "efcbd189-4aca-4240-a81a-9ce7ff157de2",
  "parameters": {
    "param1": "value1",
    "param2": "value2"
  },
  "timeout": 5000
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "Plugin execute command sent successfully"
}
```

## 插件历史和统计 API

### 8. 获取分发历史

**GET** `/plugins/{pluginId}/distributions`

获取插件的分发历史记录。

#### 路径参数
- `pluginId`: 插件ID

#### 查询参数
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| page | Number | 否 | 页码（默认1） |
| limit | Number | 否 | 每页数量（默认20） |

#### 响应示例
```json
{
  "success": true,
  "history": [
    {
      "pluginId": "00998357-8355-46d1-93b1-acd45ef0f918",
      "targetAgents": ["efcbd189-4aca-4240-a81a-9ce7ff157de2"],
      "distributionTime": "2025-08-03T12:24:57.228Z",
      "autoExecute": true,
      "priority": 1
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

### 9. 获取执行结果

**GET** `/plugins/{pluginId}/executions`

获取插件的执行结果历史。

#### 路径参数
- `pluginId`: 插件ID

#### 查询参数
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| agentId | String | 否 | 过滤特定Agent |
| startDate | String | 否 | 开始日期 |
| endDate | String | 否 | 结束日期 |
| page | Number | 否 | 页码（默认1） |
| limit | Number | 否 | 每页数量（默认20） |

#### 响应示例
```json
{
  "success": true,
  "results": [
    {
      "pluginId": "00998357-8355-46d1-93b1-acd45ef0f918",
      "agentId": "efcbd189-4aca-4240-a81a-9ce7ff157de2",
      "success": true,
      "data": {
        "plugin_name": "test_plugin",
        "version": "1.0.0",
        "status": "running"
      },
      "executionTime": 150,
      "timestamp": "2025-08-03T12:25:30.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

## 插件安全 API

### 10. 审核插件

**POST** `/plugins/{pluginId}/audit`

对插件进行安全审核。

#### 路径参数
- `pluginId`: 插件ID

#### 请求体
```json
{
  "approved": true,
  "reviewer": "TestAdmin",
  "notes": "Simple test plugin - approved for testing"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "Plugin approved successfully"
}
```

### 11. 获取插件安全状态

**GET** `/plugins/{pluginId}/security`

获取插件的安全状态信息。

#### 路径参数
- `pluginId`: 插件ID

#### 响应示例
```json
{
  "success": true,
  "security": {
    "pluginId": "00998357-8355-46d1-93b1-acd45ef0f918",
    "name": "test_plugin",
    "version": "1.0.0",
    "security": {
      "signatureValid": true,
      "integrityValid": true,
      "approved": true,
      "auditStatus": {
        "approved": true,
        "reviewer": "TestAdmin",
        "timestamp": 1754224136359,
        "auditHash": "323f726b6673d11680191d51a49f838186248074254841d2b4d411f43aa64a89",
        "notes": "Simple test plugin - approved for testing"
      },
      "uploadTime": "2025-08-03T12:24:02.642Z"
    }
  }
}
```

### 12. 验证插件签名

**GET** `/plugins/{pluginId}/verify-signature`

验证插件的数字签名。

#### 路径参数
- `pluginId`: 插件ID

#### 响应示例
```json
{
  "success": true,
  "pluginId": "00998357-8355-46d1-93b1-acd45ef0f918",
  "signatureValid": true
}
```

## Agent 管理 API

### 13. 获取所有Agent

**GET** `/agents`

获取所有连接的Agent列表。

#### 响应示例
```json
{
  "total": 1,
  "agents": [
    {
      "id": "efcbd189-4aca-4240-a81a-9ce7ff157de2",
      "status": "connected",
      "connectTime": "2025-08-03T12:20:30.982Z",
      "lastHeartbeat": "2025-08-03T12:29:52.438Z",
      "loadedPlugins": [],
      "remoteAddress": "::ffff:127.0.0.1"
    }
  ]
}
```

### 14. 获取指定Agent信息

**GET** `/agents/{agentId}`

获取指定Agent的详细信息。

#### 路径参数
- `agentId`: Agent ID

#### 响应示例
```json
{
  "id": "efcbd189-4aca-4240-a81a-9ce7ff157de2",
  "status": "connected",
  "connectTime": "2025-08-03T12:20:30.982Z",
  "lastHeartbeat": "2025-08-03T12:29:52.438Z",
  "systemInfo": {
    "hostname": "TEST-PC",
    "platform": "windows",
    "cpus": 8,
    "totalMemory": 16777216000,
    "freeMemory": **********
  },
  "loadedPlugins": [
    {
      "id": "test_plugin",
      "name": "test_plugin",
      "status": "loaded"
    }
  ],
  "remoteAddress": "::ffff:127.0.0.1"
}
```

## 系统状态 API

### 15. 健康检查

**GET** `/health`

检查系统健康状态。

#### 响应示例
```json
{
  "status": "healthy",
  "timestamp": "2025-08-03T12:30:07.290Z",
  "stats": {
    "totalConnections": 1,
    "activeConnections": 1,
    "averageResponseTime": 576.308,
    "dataTransferred": 32802,
    "errorsCount": 0
  },
  "uptime": 616.0632505,
  "memory": {
    "rss": 60928000,
    "heapTotal": 16265216,
    "heapUsed": 14061960,
    "external": 2359891,
    "arrayBuffers": 47946
  }
}
```

### 16. 获取系统统计

**GET** `/stats`

获取详细的系统统计信息。

#### 响应示例
```json
{
  "totalConnections": 1,
  "activeConnections": 1,
  "averageResponseTime": 576.308,
  "dataTransferred": 32802,
  "errorsCount": 0,
  "serverUptime": 616.0632505,
  "memoryUsage": {
    "rss": 60928000,
    "heapTotal": 16265216,
    "heapUsed": 14061960,
    "external": 2359891,
    "arrayBuffers": 47946
  },
  "timestamp": "2025-08-03T12:30:07.290Z"
}
```

## 错误代码

| HTTP状态码 | 描述 |
|-----------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用限制

- 插件文件大小限制：50MB
- 支持的插件格式：.dll（Windows）、.so（Linux）、.dylib（macOS）
- 最大连接数：可配置（默认无限制）
- 请求超时：30秒

## 安全注意事项

1. **插件验证**：所有上传的插件都会进行校验和验证
2. **访问控制**：只能向在线的已知Agent分发插件
3. **审核流程**：建议对所有插件进行安全审核后再分发
4. **传输安全**：建议在生产环境中使用HTTPS
5. **日志监控**：系统会记录所有操作日志

## 示例工作流

### 典型的插件分发流程

1. **上传插件**
   ```bash
   curl -X POST -F "plugin=@my_plugin.dll" -F "name=my_plugin" -F "version=1.0.0" -F "category=custom" http://localhost:3000/api/plugins/upload
   ```

2. **审核插件**
   ```bash
   curl -X POST -H "Content-Type: application/json" -d '{"approved":true,"reviewer":"Admin","notes":"Approved"}' http://localhost:3000/api/plugins/{pluginId}/audit
   ```

3. **分发插件**
   ```bash
   curl -X POST -H "Content-Type: application/json" -d '{"agentId":"target-agent-id","autoExecute":true}' http://localhost:3000/api/plugins/{pluginId}/distribute
   ```

4. **监控执行**
   ```bash
   curl http://localhost:3000/api/plugins/{pluginId}/executions
   ```

这套API为分布式监控系统提供了完整的插件管理能力，支持企业级的安全控制和运维管理需求。