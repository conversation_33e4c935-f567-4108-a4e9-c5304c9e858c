import { Request, Response } from 'express';
import { PluginService } from '../services/PluginService';
import { ConnectionManager } from '../services/ConnectionManager';
import { MessageType } from '../types/Message';
import logger from '@utils/logger';
import { ValidationHelper } from '@utils/helpers';

export class PluginController {
  private pluginService: PluginService;
  private connectionManager: ConnectionManager;

  constructor() {
    this.pluginService = PluginService.getInstance();
    this.connectionManager = ConnectionManager.getInstance();
  }

  /**
   * 上传插件文件
   */
  public uploadPlugin = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({ error: 'No plugin file provided' });
        return;
      }

      const { name, version, description, author, category, targetSystems, dependencies } = req.body;

      if (!name || !version || !category) {
        res.status(400).json({ error: 'Missing required plugin metadata' });
        return;
      }

      const pluginInfo = await this.pluginService.uploadPlugin({
        name: ValidationHelper.sanitizeString(name),
        version: ValidationHelper.sanitizeString(version),
        description: description ? ValidationHelper.sanitizeString(description) : '',
        author: author ? ValidationHelper.sanitizeString(author) : 'Unknown',
        category,
        targetSystems: targetSystems ? JSON.parse(targetSystems) : ['all'],
        dependencies: dependencies ? JSON.parse(dependencies) : [],
        fileBuffer: req.file.buffer,
        fileName: req.file.originalname
      });

      logger.info(`Plugin uploaded successfully: ${pluginInfo.name} (${pluginInfo.id})`);
      res.status(201).json({ 
        success: true, 
        plugin: pluginInfo 
      });

    } catch (error) {
      logger.logError(error as Error, 'Plugin upload failed');
      res.status(500).json({ 
        error: 'Failed to upload plugin',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 获取所有插件列表
   */
  public getAllPlugins = async (req: Request, res: Response): Promise<void> => {
    try {
      const plugins = await this.pluginService.getAllPlugins();
      res.json({ 
        success: true, 
        plugins,
        count: plugins.length 
      });
    } catch (error) {
      logger.logError(error as Error, 'Failed to get plugins');
      res.status(500).json({ 
        error: 'Failed to retrieve plugins',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 获取指定插件信息
   */
  public getPlugin = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }
      const plugin = await this.pluginService.getPluginById(id);
      
      if (!plugin) {
        res.status(404).json({ error: 'Plugin not found' });
        return;
      }

      res.json({ 
        success: true, 
        plugin 
      });
    } catch (error) {
      logger.logError(error as Error, 'Failed to get plugin');
      res.status(500).json({ 
        error: 'Failed to retrieve plugin',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 删除插件
   */
  public deletePlugin = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }
      const success = await this.pluginService.deletePlugin(id);
      
      if (!success) {
        res.status(404).json({ error: 'Plugin not found' });
        return;
      }

      logger.info(`Plugin deleted: ${id}`);
      res.json({ 
        success: true, 
        message: 'Plugin deleted successfully' 
      });
    } catch (error) {
      logger.logError(error as Error, 'Failed to delete plugin');
      res.status(500).json({ 
        error: 'Failed to delete plugin',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 向指定Agent分发插件
   */
  public distributeToAgent = async (req: Request, res: Response): Promise<void> => {
    try {
      const { pluginId } = req.params;
      const { agentId, autoExecute = false, priority = 1 } = req.body;

      if (!pluginId) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      if (!agentId) {
        res.status(400).json({ error: 'Agent ID is required' });
        return;
      }

      const sanitizedAgentId = ValidationHelper.sanitizeString(agentId);

      // 检查Agent是否在线
      const connection = this.connectionManager.getConnection(sanitizedAgentId);
      if (!connection) {
        res.status(404).json({ error: 'Agent not found or offline' });
        return;
      }

      const result = await this.pluginService.distributePluginToAgent(
        pluginId, 
        sanitizedAgentId, 
        autoExecute, 
        priority
      );

      if (!result.success) {
        res.status(400).json({ 
          error: 'Failed to distribute plugin',
          details: result.error 
        });
        return;
      }

      logger.info(`Plugin ${pluginId} distributed to agent ${sanitizedAgentId}`);
      res.json({ 
        success: true, 
        message: 'Plugin distributed successfully',
        distributionId: result.distributionId 
      });

    } catch (error) {
      logger.logError(error as Error, 'Plugin distribution failed');
      res.status(500).json({ 
        error: 'Failed to distribute plugin',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 向多个Agent批量分发插件
   */
  public distributeToBatch = async (req: Request, res: Response): Promise<void> => {
    try {
      const { pluginId } = req.params;
      const { agentIds, autoExecute = false, priority = 1 } = req.body;

      if (!pluginId) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      if (!Array.isArray(agentIds) || agentIds.length === 0) {
        res.status(400).json({ error: 'Agent IDs array is required' });
        return;
      }

      const results = await this.pluginService.distributeToBatchAgents(
        pluginId,
        agentIds,
        autoExecute,
        priority
      );

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      logger.info(`Plugin ${pluginId} batch distribution: ${successCount} success, ${failureCount} failed`);
      
      res.json({
        success: true,
        message: `Plugin distributed to ${successCount}/${results.length} agents`,
        results: results,
        summary: {
          total: results.length,
          success: successCount,
          failed: failureCount
        }
      });

    } catch (error) {
      logger.logError(error as Error, 'Batch plugin distribution failed');
      res.status(500).json({ 
        error: 'Failed to distribute plugin to batch',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 向指定Agent发送插件执行命令
   */
  public executePlugin = async (req: Request, res: Response): Promise<void> => {
    try {
      const { pluginId } = req.params;
      const { agentId, parameters, timeout } = req.body;

      if (!pluginId) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      if (!agentId) {
        res.status(400).json({ error: 'Agent ID is required' });
        return;
      }

      const sanitizedAgentId = ValidationHelper.sanitizeString(agentId);

      // 检查Agent是否在线
      const connection = this.connectionManager.getConnection(sanitizedAgentId);
      if (!connection) {
        res.status(404).json({ error: 'Agent not found or offline' });
        return;
      }

      const success = await this.connectionManager.sendToAgent(sanitizedAgentId, {
        type: MessageType.PLUGIN_EXECUTE,
        timestamp: Date.now(),
        agentId: sanitizedAgentId,
        messageId: `execute-${pluginId}-${Date.now()}`,
        data: {
          pluginId,
          parameters,
          timeout
        }
      });

      if (!success) {
        res.status(500).json({ error: 'Failed to send execute command to agent' });
        return;
      }

      logger.info(`Plugin execute command sent: ${pluginId} to agent ${sanitizedAgentId}`);
      res.json({ 
        success: true, 
        message: 'Plugin execute command sent successfully' 
      });

    } catch (error) {
      logger.logError(error as Error, 'Plugin execution command failed');
      res.status(500).json({ 
        error: 'Failed to execute plugin',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 获取插件分发历史
   */
  public getDistributionHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { pluginId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      if (!pluginId) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      const history = await this.pluginService.getDistributionHistory(
        pluginId,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json({
        success: true,
        history: history.records,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: history.total,
          pages: Math.ceil(history.total / parseInt(limit as string))
        }
      });

    } catch (error) {
      logger.logError(error as Error, 'Failed to get distribution history');
      res.status(500).json({ 
        error: 'Failed to retrieve distribution history',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 获取插件执行结果
   */
  public getExecutionResults = async (req: Request, res: Response): Promise<void> => {
    try {
      const { pluginId } = req.params;
      const { agentId, startDate, endDate, page = 1, limit = 20 } = req.query;

      if (!pluginId) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      const results = await this.pluginService.getExecutionResults({
        pluginId,
        agentId: agentId ? ValidationHelper.sanitizeString(agentId as string) : undefined,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      });

      res.json({
        success: true,
        results: results.records,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: results.total,
          pages: Math.ceil(results.total / parseInt(limit as string))
        }
      });

    } catch (error) {
      logger.logError(error as Error, 'Failed to get execution results');
      res.status(500).json({ 
        error: 'Failed to retrieve execution results',
        details: (error as Error).message 
      });
    }
  };

  /**
   * 审核插件
   */
  public auditPlugin = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { approved, reviewer, notes } = req.body;

      if (!id) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      if (typeof approved !== 'boolean' || !reviewer) {
        res.status(400).json({ error: 'Approval status and reviewer are required' });
        return;
      }

      const auditRequest: any = {
        approved,
        reviewer: ValidationHelper.sanitizeString(reviewer)
      };
      
      if (notes) {
        auditRequest.notes = ValidationHelper.sanitizeString(notes);
      }

      const success = await this.pluginService.auditPlugin(id, auditRequest);

      if (!success) {
        res.status(404).json({ error: 'Plugin not found or audit failed' });
        return;
      }

      logger.info(`Plugin ${id} audit completed by ${reviewer}: ${approved ? 'approved' : 'rejected'}`);
      res.json({
        success: true,
        message: `Plugin ${approved ? 'approved' : 'rejected'} successfully`
      });

    } catch (error) {
      logger.logError(error as Error, 'Plugin audit failed');
      res.status(500).json({
        error: 'Failed to audit plugin',
        details: (error as Error).message
      });
    }
  };

  /**
   * 获取插件安全状态
   */
  public getPluginSecurity = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      const securityStatus = await this.pluginService.getPluginSecurityStatus(id);

      if (!securityStatus) {
        res.status(404).json({ error: 'Plugin not found' });
        return;
      }

      res.json({
        success: true,
        security: securityStatus
      });

    } catch (error) {
      logger.logError(error as Error, 'Failed to get plugin security status');
      res.status(500).json({
        error: 'Failed to retrieve plugin security status',
        details: (error as Error).message
      });
    }
  };

  /**
   * 验证插件签名
   */
  public verifyPluginSignature = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({ error: 'Plugin ID is required' });
        return;
      }

      const isValid = await this.pluginService.verifyPluginSignature(id);

      res.json({
        success: true,
        pluginId: id,
        signatureValid: isValid
      });

    } catch (error) {
      logger.logError(error as Error, 'Plugin signature verification failed');
      res.status(500).json({
        error: 'Failed to verify plugin signature',
        details: (error as Error).message
      });
    }
  };

  /**
   * 向Agent发送文件管理命令
   */
  public sendFileManagerCommand = async (req: Request, res: Response): Promise<void> => {
    try {
      const { agentId } = req.params;
      const { command, path, dest_path, data, pattern } = req.body;

      if (!agentId) {
        res.status(400).json({ error: 'Agent ID is required' });
        return;
      }

      if (!command) {
        res.status(400).json({ error: 'Command is required' });
        return;
      }

      // 构建文件管理命令JSON
      const fileManagerCommand = {
        command: ValidationHelper.sanitizeString(command),
        ...(path && { path: ValidationHelper.sanitizeString(path) }),
        ...(dest_path && { dest_path: ValidationHelper.sanitizeString(dest_path) }),
        ...(data && { data: ValidationHelper.sanitizeString(data) }),
        ...(pattern && { pattern: ValidationHelper.sanitizeString(pattern) })
      };

      // 发送插件命令消息给Agent
      const agent = this.connectionManager.getConnection(agentId);
      if (!agent) {
        res.status(404).json({ error: 'Agent not found or not connected' });
        return;
      }

      // 使用正确的消息格式通过ConnectionManager发送
      const success = await this.connectionManager.sendToAgent(agentId, {
        type: 'plugin_command' as any,
        timestamp: Date.now(),
        agentId: agentId,
        messageId: `file-manager-${Date.now()}`,
        data: {
          pluginName: 'file_manager',
          commandData: JSON.stringify(fileManagerCommand)
        }
      });

      if (!success) {
        res.status(500).json({ error: 'Failed to send command to agent' });
        return;
      }

      logger.info(`File manager command sent to agent ${agentId}: ${command}`);
      res.json({
        success: true,
        message: 'File manager command sent successfully',
        command: fileManagerCommand
      });

    } catch (error) {
      logger.logError(error as Error, 'Failed to send file manager command');
      res.status(500).json({
        error: 'Failed to send file manager command',
        details: (error as Error).message
      });
    }
  };
}