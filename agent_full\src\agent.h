#ifndef AGENT_H
#define AGENT_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #define SLEEP_MS(ms) Sleep(ms)
    #define GET_LAST_ERROR() WSAGetLastError()
    #define PATH_SEPARATOR "\\"
#else
    #include <unistd.h>
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <netdb.h>
    #include <dlfcn.h>
    #define SLEEP_MS(ms) usleep((ms) * 1000)
    #define GET_LAST_ERROR() errno
    #define PATH_SEPARATOR "/"
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

// Constants
#define MAX_BUFFER_SIZE 8192
#define MAX_MESSAGE_SIZE 4096
#define MAX_HOSTNAME_LEN 256
#define MAX_PATH_LEN 512
#define MAX_AGENT_ID_LEN 64
#define MAX_PLUGINS 16
#define HEARTBEAT_INTERVAL_MS 30000
#define RECONNECT_INTERVAL_MS 5000
#define MAX_RECONNECT_ATTEMPTS 10

// Error codes
typedef enum {
    AGENT_SUCCESS = 0,
    AGENT_ERROR_INVALID_PARAM = -1,
    AGENT_ERROR_MEMORY = -2,
    AGENT_ERROR_NETWORK = -3,
    AGENT_ERROR_WEBSOCKET = -4,
    AGENT_ERROR_CONFIG = -5,
    AGENT_ERROR_SYSTEM = -6,
    AGENT_ERROR_PLUGIN = -7,
    AGENT_ERROR_NOT_FOUND = -8,
    AGENT_ERROR_TIMEOUT = -9
} AgentResult;

// Log levels
typedef enum {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARN = 2,
    LOG_ERROR = 3
} LogLevel;

// Agent state
typedef enum {
    AGENT_STATE_DISCONNECTED = 0,
    AGENT_STATE_CONNECTING = 1,
    AGENT_STATE_CONNECTED = 2,
    AGENT_STATE_ERROR = 3,
    AGENT_STATE_SHUTDOWN = 4
} AgentState;

// Plugin state
typedef enum {
    PLUGIN_STATE_UNLOADED = 0,
    PLUGIN_STATE_LOADED = 1,
    PLUGIN_STATE_RUNNING = 2,
    PLUGIN_STATE_ERROR = 3
} PluginState;

// Plugin info
typedef struct {
    char name[64];
    char path[MAX_PATH_LEN];
    char plugin_id[64];
    PluginState state;
    void* handle;
    uint64_t last_execution;
    int execution_interval;
    char last_error[256];
    int is_remote;
    
    // Plugin interface functions
    int (*init_func)(void);
    int (*execute_func)(void);
    int (*cleanup_func)(void);
    char* (*get_data_func)(void);
} PluginInfo;

// Agent configuration
typedef struct {
    char server_host[MAX_HOSTNAME_LEN];
    int server_port;
    char agent_id[MAX_AGENT_ID_LEN];
    int heartbeat_interval;
    int reconnect_interval;
    int max_reconnect_attempts;
    LogLevel log_level;
    char plugin_directory[MAX_PATH_LEN];
    int plugin_scan_interval;
} AgentConfig;

// Agent statistics
typedef struct {
    uint64_t start_time;
    uint64_t uptime;
    uint64_t messages_sent;
    uint64_t messages_received;
    uint64_t bytes_sent;
    uint64_t bytes_received;
    int reconnect_count;
    int plugin_count;
    int error_count;
    uint32_t memory_usage;
    float cpu_usage;
} AgentStats;

// Agent context
typedef struct {
    AgentConfig config;
    AgentState state;
    AgentStats stats;
    SOCKET websocket;
    int reconnect_count;
    uint64_t last_heartbeat;
    uint64_t start_time;
    bool running;
    char last_error[256];
    
    // Plugin management
    PluginInfo plugins[MAX_PLUGINS];
    int plugin_count;
    uint64_t last_plugin_scan;
} AgentContext;

// Global functions
uint64_t get_timestamp_ms(void);
void format_timestamp(char* buffer, size_t size);
void agent_set_error(AgentContext* ctx, AgentResult error_code, const char* message);

#endif // AGENT_H