#ifndef FILE_WATCHER_H
#define FILE_WATCHER_H

#include "common.h"

#ifdef _WIN32
#include <windows.h>
#include <winnt.h>
#endif

// 文件监控事件标志
#define FILE_WATCH_CREATED      0x01
#define FILE_WATCH_MODIFIED     0x02
#define FILE_WATCH_DELETED      0x04
#define FILE_WATCH_RENAMED      0x08
#define FILE_WATCH_ACCESSED     0x10
#define FILE_WATCH_ATTRIBUTES   0x20
#define FILE_WATCH_SIZE         0x40
#define FILE_WATCH_ALL          0xFF

// 监控路径信息
typedef struct {
    char path[MAX_PATH_LENGTH];
    bool recursive;
    uint32_t watch_flags;
    uint64_t last_check_time;
    
    #ifdef _WIN32
    HANDLE directory_handle;
    OVERLAPPED overlapped;
    BYTE buffer[8192];
    DWORD bytes_returned;
    #endif
    
} WatchPath;

// 文件监控器状态
typedef enum {
    FILE_WATCHER_STATE_UNINITIALIZED = 0,
    FILE_WATCHER_STATE_INITIALIZED = 1,
    FILE_WATCHER_STATE_WATCHING = 2,
    FILE_WATCHER_STATE_STOPPED = 3,
    FILE_WATCHER_STATE_ERROR = 4
} FileWatcherState;

// 文件监控器配置
typedef struct {
    uint32_t buffer_size;
    uint32_t max_watch_paths;
    uint32_t polling_interval_ms;
    bool use_completion_ports;
    bool notify_filter_all;
} FileWatcherConfig;

// 原始文件事件（平台特定）
typedef struct {
    uint32_t action;
    char file_name[MAX_PATH_LENGTH];
    char old_file_name[MAX_PATH_LENGTH];
    uint64_t event_time;
    WatchPath* watch_path;
} RawFileEvent;

// 文件监控器统计
typedef struct {
    uint64_t total_events;
    uint64_t events_processed;
    uint64_t events_dropped;
    uint64_t watch_paths_active;
    uint64_t bytes_read;
    uint32_t last_error;
    char last_error_message[256];
} FileWatcherStats;

// 文件监控器上下文
typedef struct {
    FileWatcherState state;
    FileWatcherConfig config;
    FileWatcherStats stats;
    
    // 监控路径列表
    WatchPath* watch_paths;
    size_t watch_path_count;
    size_t max_watch_paths;
    
    // 事件处理
    void (*event_callback)(const RawFileEvent* event, void* user_data);
    void* callback_user_data;
    
    #ifdef _WIN32
    // Windows特定资源
    HANDLE completion_port;
    HANDLE* worker_threads;
    size_t worker_thread_count;
    volatile bool should_stop;
    CRITICAL_SECTION paths_mutex;
    #endif
    
    // 错误信息
    int last_error;
    char error_message[256];
    
} FileWatcherContext;

// 主要接口函数
int file_watcher_init(FileWatcherContext** context, const FileWatcherConfig* config);
void file_watcher_cleanup(FileWatcherContext* context);
int file_watcher_start(FileWatcherContext* context);
int file_watcher_stop(FileWatcherContext* context);

// 路径管理
int file_watcher_add_path(FileWatcherContext* context, const char* path, bool recursive, uint32_t watch_flags);
int file_watcher_remove_path(FileWatcherContext* context, const char* path);
int file_watcher_clear_paths(FileWatcherContext* context);
WatchPath* file_watcher_find_path(FileWatcherContext* context, const char* path);

// 事件处理
int file_watcher_set_event_callback(FileWatcherContext* context, 
                                   void (*callback)(const RawFileEvent*, void*), 
                                   void* user_data);
int file_watcher_process_events(FileWatcherContext* context);

// 状态查询
FileWatcherState file_watcher_get_state(FileWatcherContext* context);
void file_watcher_get_stats(FileWatcherContext* context, FileWatcherStats* stats);
const char* file_watcher_get_last_error_message(FileWatcherContext* context);

// 配置管理
int file_watcher_set_config(FileWatcherContext* context, const FileWatcherConfig* config);
void file_watcher_get_config(FileWatcherContext* context, FileWatcherConfig* config);

#ifdef _WIN32
// Windows特定实现
int file_watcher_setup_directory_monitoring(FileWatcherContext* context, WatchPath* watch_path);
int file_watcher_start_async_read(FileWatcherContext* context, WatchPath* watch_path);
DWORD WINAPI file_watcher_worker_thread(LPVOID param);
void file_watcher_process_directory_changes(FileWatcherContext* context, WatchPath* watch_path, DWORD bytes_transferred);
uint32_t file_watcher_convert_windows_action(DWORD action);

// Windows通知过滤器
#define FILE_NOTIFY_CHANGE_FILE_NAME   0x00000001
#define FILE_NOTIFY_CHANGE_DIR_NAME    0x00000002
#define FILE_NOTIFY_CHANGE_ATTRIBUTES  0x00000004
#define FILE_NOTIFY_CHANGE_SIZE        0x00000008
#define FILE_NOTIFY_CHANGE_LAST_WRITE  0x00000010
#define FILE_NOTIFY_CHANGE_LAST_ACCESS 0x00000020
#define FILE_NOTIFY_CHANGE_CREATION    0x00000040
#define FILE_NOTIFY_CHANGE_SECURITY    0x00000100

DWORD file_watcher_build_notify_filter(uint32_t watch_flags);
#endif

// 工具函数
const char* file_watcher_action_to_string(uint32_t action);
const char* file_watcher_state_to_string(FileWatcherState state);
bool file_watcher_is_valid_path(const char* path);
int file_watcher_normalize_path(const char* input_path, char* normalized_path, size_t buffer_size);

// 调试和诊断
void file_watcher_dump_paths(FileWatcherContext* context);
void file_watcher_dump_stats(FileWatcherContext* context);

#endif // FILE_WATCHER_H