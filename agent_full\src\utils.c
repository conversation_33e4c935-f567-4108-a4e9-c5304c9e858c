#include "utils.h"
#include <sys/stat.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/time.h>
#endif

uint64_t get_timestamp_ms(void)
{
#ifdef _WIN32
    return GetTickCount64();
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
#endif
}

void format_timestamp(char* buffer, size_t size)
{
    if (!buffer || size == 0) return;
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

char* create_json_message(const char* type, const char* agent_id, const char* data)
{
    if (!type || !agent_id) return NULL;
    
    size_t msg_size = 512 + (data ? strlen(data) : 0);
    char* message = malloc(msg_size);
    if (!message) return NULL;
    
    // Generate unique message ID
    char message_id[64];
    snprintf(message_id, sizeof(message_id), "%s-%llu", agent_id, get_timestamp_ms());
    
    // Get timestamp as number (milliseconds since epoch)
    uint64_t timestamp_ms = get_timestamp_ms();
    
    if (data) {
        snprintf(message, msg_size,
            "{"
            "\"type\":\"%s\","
            "\"timestamp\":%llu,"
            "\"agentId\":\"%s\","
            "\"messageId\":\"%s\","
            "\"data\":%s"
            "}", type, timestamp_ms, agent_id, message_id, data);
    } else {
        snprintf(message, msg_size,
            "{"
            "\"type\":\"%s\","
            "\"timestamp\":%llu,"
            "\"agentId\":\"%s\","
            "\"messageId\":\"%s\","
            "\"data\":{}"
            "}", type, timestamp_ms, agent_id, message_id);
    }
    
    return message;
}

void free_json_message(char* message)
{
    if (message) {
        free(message);
    }
}

void get_system_info(char* buffer, size_t size)
{
    if (!buffer || size == 0) return;
    
    char hostname[MAX_HOSTNAME_LEN] = "unknown";
    
#ifdef _WIN32
    DWORD hostname_size = sizeof(hostname);
    GetComputerNameA(hostname, &hostname_size);
    
    SYSTEM_INFO sys_info;
    GetSystemInfo(&sys_info);
    
    MEMORYSTATUSEX mem_info;
    mem_info.dwLength = sizeof(mem_info);
    GlobalMemoryStatusEx(&mem_info);
    
    snprintf(buffer, size,
        "{"
        "\"hostname\":\"%s\","
        "\"platform\":\"windows\","
        "\"cpus\":%lu,"
        "\"totalMemory\":%llu,"
        "\"freeMemory\":%llu"
        "}",
        hostname,
        sys_info.dwNumberOfProcessors,
        mem_info.ullTotalPhys,
        mem_info.ullAvailPhys);
#else
    gethostname(hostname, sizeof(hostname));
    
    snprintf(buffer, size,
        "{"
        "\"hostname\":\"%s\","
        "\"platform\":\"unix\","
        "\"cpus\":1,"
        "\"totalMemory\":0,"
        "\"freeMemory\":0"
        "}",
        hostname);
#endif
}

// Base64 decoding table
static const int b64_decode_table[256] = {
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,62, -1,-1,-1,63,
    52,53,54,55, 56,57,58,59, 60,61,-1,-1, -1,-2,-1,-1,
    -1, 0, 1, 2,  3, 4, 5, 6,  7, 8, 9,10, 11,12,13,14,
    15,16,17,18, 19,20,21,22, 23,24,25,-1, -1,-1,-1,-1,
    -1,26,27,28, 29,30,31,32, 33,34,35,36, 37,38,39,40,
    41,42,43,44, 45,46,47,48, 49,50,51,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
    -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1
};

unsigned char* base64_decode(const char* input, size_t* output_length)
{
    if (!input || !output_length) {
        return NULL;
    }
    
    size_t input_length = strlen(input);
    if (input_length % 4 != 0) {
        return NULL;
    }
    
    *output_length = input_length / 4 * 3;
    if (input[input_length - 1] == '=') (*output_length)--;
    if (input[input_length - 2] == '=') (*output_length)--;
    
    unsigned char* output = malloc(*output_length);
    if (!output) {
        return NULL;
    }
    
    for (size_t i = 0, j = 0; i < input_length;) {
        uint32_t a = input[i] == '=' ? 0 & i++ : b64_decode_table[(int)input[i++]];
        uint32_t b = input[i] == '=' ? 0 & i++ : b64_decode_table[(int)input[i++]];
        uint32_t c = input[i] == '=' ? 0 & i++ : b64_decode_table[(int)input[i++]];
        uint32_t d = input[i] == '=' ? 0 & i++ : b64_decode_table[(int)input[i++]];
        
        uint32_t triple = (a << 18) + (b << 12) + (c << 6) + d;
        
        if (j < *output_length) output[j++] = (triple >> 16) & 0xFF;
        if (j < *output_length) output[j++] = (triple >> 8) & 0xFF;
        if (j < *output_length) output[j++] = triple & 0xFF;
    }
    
    return output;
}

char* calculate_file_checksum(const unsigned char* data, size_t length)
{
    if (!data || length == 0) {
        return NULL;
    }
    
    // Simple checksum calculation (in real implementation, use SHA256)
    uint32_t checksum = 0;
    for (size_t i = 0; i < length; i++) {
        checksum = ((checksum << 5) + checksum) + data[i];
    }
    
    char* result = malloc(16);
    if (result) {
        snprintf(result, 16, "%08x", checksum);
    }
    
    return result;
}

int verify_checksum(const unsigned char* data, size_t length, const char* expected_checksum)
{
    if (!data || !expected_checksum) {
        return 0;
    }
    
    char* calculated = calculate_file_checksum(data, length);
    if (!calculated) {
        return 0;
    }
    
    int result = (strcmp(calculated, expected_checksum) == 0);
    free(calculated);
    
    return result;
}