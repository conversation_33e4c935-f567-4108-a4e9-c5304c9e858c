#include "utils.h"
#include <sys/stat.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/time.h>
#endif

uint64_t get_timestamp_ms(void)
{
#ifdef _WIN32
    return GetTickCount64();
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
#endif
}

void format_timestamp(char* buffer, size_t size)
{
    if (!buffer || size == 0) return;
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

char* create_json_message(const char* type, const char* agent_id, const char* data)
{
    if (!type || !agent_id) return NULL;
    
    size_t msg_size = 512 + (data ? strlen(data) : 0);
    char* message = malloc(msg_size);
    if (!message) return NULL;
    
    // Generate unique message ID
    char message_id[64];
    snprintf(message_id, sizeof(message_id), "%s-%llu", agent_id, get_timestamp_ms());
    
    // Get timestamp as number (milliseconds since epoch)
    uint64_t timestamp_ms = get_timestamp_ms();
    
    if (data) {
        snprintf(message, msg_size,
            "{"
            "\"type\":\"%s\","
            "\"timestamp\":%llu,"
            "\"agentId\":\"%s\","
            "\"messageId\":\"%s\","
            "\"data\":%s"
            "}", type, timestamp_ms, agent_id, message_id, data);
    } else {
        snprintf(message, msg_size,
            "{"
            "\"type\":\"%s\","
            "\"timestamp\":%llu,"
            "\"agentId\":\"%s\","
            "\"messageId\":\"%s\","
            "\"data\":{}"
            "}", type, timestamp_ms, agent_id, message_id);
    }
    
    return message;
}

void free_json_message(char* message)
{
    if (message) {
        free(message);
    }
}

void get_system_info(char* buffer, size_t size)
{
    if (!buffer || size == 0) return;
    
    char hostname[MAX_HOSTNAME_LEN] = "unknown";
    
#ifdef _WIN32
    DWORD hostname_size = sizeof(hostname);
    GetComputerNameA(hostname, &hostname_size);
    
    SYSTEM_INFO sys_info;
    GetSystemInfo(&sys_info);
    
    MEMORYSTATUSEX mem_info;
    mem_info.dwLength = sizeof(mem_info);
    GlobalMemoryStatusEx(&mem_info);
    
    snprintf(buffer, size,
        "{"
        "\"hostname\":\"%s\","
        "\"platform\":\"windows\","
        "\"cpus\":%lu,"
        "\"totalMemory\":%llu,"
        "\"freeMemory\":%llu"
        "}",
        hostname,
        sys_info.dwNumberOfProcessors,
        mem_info.ullTotalPhys,
        mem_info.ullAvailPhys);
#else
    gethostname(hostname, sizeof(hostname));
    
    snprintf(buffer, size,
        "{"
        "\"hostname\":\"%s\","
        "\"platform\":\"unix\","
        "\"cpus\":1,"
        "\"totalMemory\":0,"
        "\"freeMemory\":0"
        "}",
        hostname);
#endif
}