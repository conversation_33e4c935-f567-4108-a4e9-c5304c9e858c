import crypto from 'crypto';

export class XOREncryption {
  private static readonly DEFAULT_KEY_LENGTH = 32;
  
  /**
   * 生成随机密钥
   */
  public static generateKey(length: number = XOREncryption.DEFAULT_KEY_LENGTH): Buffer {
    return crypto.randomBytes(length);
  }
  
  /**
   * XOR加密/解密
   * @param data 要加密或解密的数据
   * @param key 密钥
   * @returns 加密或解密后的数据
   */
  public static encrypt(data: Buffer, key: Buffer): Buffer {
    const result = Buffer.alloc(data.length);
    const keyLength = key.length;
    
    for (let i = 0; i < data.length; i++) {
      result[i] = data[i]! ^ key[i % keyLength]!;
    }
    
    return result;
  }
  
  /**
   * XOR解密（同加密函数）
   */
  public static decrypt(data: Buffer, key: Buffer): Buffer {
    return XOREncryption.encrypt(data, key);
  }
  
  /**
   * 字符串XOR加密
   */
  public static encryptString(text: string, key: Buffer): string {
    const data = Buffer.from(text, 'utf8');
    const encrypted = XOREncryption.encrypt(data, key);
    return encrypted.toString('base64');
  }
  
  /**
   * 字符串XOR解密
   */
  public static decryptString(encryptedText: string, key: Buffer): string {
    const data = Buffer.from(encryptedText, 'base64');
    const decrypted = XOREncryption.decrypt(data, key);
    return decrypted.toString('utf8');
  }
}

export class KeyExchange {
  /**
   * 生成密钥交换参数
   */
  public static generateKeyExchangeData(): {
    clientRandom: Buffer;
    serverRandom: Buffer;
    sessionKey: Buffer;
  } {
    const clientRandom = crypto.randomBytes(16);
    const serverRandom = crypto.randomBytes(16);
    
    // 简单的密钥派生（实际项目中应使用更安全的方法）
    const combinedData = Buffer.concat([clientRandom, serverRandom]);
    const sessionKey = crypto.createHash('sha256').update(combinedData).digest().slice(0, 32);
    
    return {
      clientRandom,
      serverRandom,
      sessionKey
    };
  }
  
  /**
   * 从客户端和服务器随机数生成会话密钥
   */
  public static deriveSessionKey(clientRandom: Buffer, serverRandom: Buffer): Buffer {
    const combinedData = Buffer.concat([clientRandom, serverRandom]);
    return crypto.createHash('sha256').update(combinedData).digest().slice(0, 32);
  }
}

export class MessageIntegrity {
  /**
   * 计算消息校验和
   */
  public static calculateChecksum(data: string | Buffer): string {
    const input = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;
    return crypto.createHash('sha256').update(input).digest('hex');
  }
  
  /**
   * 验证消息完整性
   */
  public static verifyChecksum(data: string | Buffer, expectedChecksum: string): boolean {
    const actualChecksum = MessageIntegrity.calculateChecksum(data);
    return actualChecksum === expectedChecksum;
  }
  
  /**
   * 生成带时间戳的签名（防重放攻击）
   */
  public static generateTimestampedSignature(data: string, key: Buffer, timestamp?: number): string {
    const ts = timestamp || Date.now();
    const signData = `${data}:${ts}`;
    const hash = crypto.createHmac('sha256', key).update(signData).digest('hex');
    return `${hash}:${ts}`;
  }
  
  /**
   * 验证带时间戳的签名
   */
  public static verifyTimestampedSignature(
    data: string, 
    signature: string, 
    key: Buffer, 
    maxAge: number = 300000 // 5分钟
  ): boolean {
    try {
      const parts = signature.split(':');
      if (parts.length !== 2) {
        return false;
      }
      const [hash, timestampStr] = parts;
      if (!hash || !timestampStr) {
        return false;
      }
      const timestamp = parseInt(timestampStr, 10);
      
      // 检查时间戳有效性
      const now = Date.now();
      if (now - timestamp > maxAge) {
        return false;
      }
      
      // 验证签名
      const expectedSignature = MessageIntegrity.generateTimestampedSignature(data, key, timestamp);
      const [expectedHash] = expectedSignature.split(':');
      
      return hash === expectedHash;
    } catch (error) {
      return false;
    }
  }
}

export class SecureRandom {
  /**
   * 生成安全的随机字符串
   */
  public static generateId(length: number = 16): string {
    return crypto.randomBytes(length).toString('hex');
  }
  
  /**
   * 生成UUID v4
   */
  public static generateUUID(): string {
    return crypto.randomUUID();
  }
  
  /**
   * 生成随机整数
   */
  public static randomInt(min: number, max: number): number {
    return crypto.randomInt(min, max + 1);
  }
}

export class PluginSecurity {
  private static readonly SIGNATURE_ALGORITHM = 'sha256';
  private static readonly PLUGIN_SIGNING_KEY = process.env.PLUGIN_SIGNING_KEY || 'default-signing-key';
  
  /**
   * 计算插件文件的SHA256哈希
   */
  public static calculatePluginHash(pluginData: Buffer): string {
    return crypto.createHash('sha256').update(pluginData).digest('hex');
  }
  
  /**
   * 为插件生成数字签名
   */
  public static signPlugin(pluginData: Buffer, metadata: any): string {
    const hash = PluginSecurity.calculatePluginHash(pluginData);
    const signData = JSON.stringify({
      hash,
      metadata: {
        name: metadata.name,
        version: metadata.version,
        author: metadata.author,
        timestamp: Date.now()
      }
    });
    
    return crypto
      .createHmac(PluginSecurity.SIGNATURE_ALGORITHM, PluginSecurity.PLUGIN_SIGNING_KEY)
      .update(signData)
      .digest('hex');
  }
  
  /**
   * 验证插件签名
   */
  public static verifyPluginSignature(
    pluginData: Buffer,
    metadata: any,
    signature: string
  ): boolean {
    try {
      const expectedSignature = PluginSecurity.signPlugin(pluginData, metadata);
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      return false;
    }
  }
  
  /**
   * 检查插件是否被篡改
   */
  public static validatePluginIntegrity(
    pluginData: Buffer,
    expectedHash: string
  ): boolean {
    const actualHash = PluginSecurity.calculatePluginHash(pluginData);
    return crypto.timingSafeEqual(
      Buffer.from(actualHash, 'hex'),
      Buffer.from(expectedHash, 'hex')
    );
  }
  
  /**
   * 生成插件分发令牌（用于授权特定Agent下载插件）
   */
  public static generateDistributionToken(
    pluginId: string,
    agentId: string,
    expiresIn: number = 3600000 // 1小时
  ): string {
    const payload = {
      pluginId,
      agentId,
      expires: Date.now() + expiresIn,
      nonce: SecureRandom.generateId(8)
    };
    
    const token = Buffer.from(JSON.stringify(payload)).toString('base64');
    const signature = crypto
      .createHmac('sha256', PluginSecurity.PLUGIN_SIGNING_KEY)
      .update(token)
      .digest('hex');
    
    return `${token}.${signature}`;
  }
  
  /**
   * 验证插件分发令牌
   */
  public static verifyDistributionToken(
    token: string,
    expectedPluginId?: string,
    expectedAgentId?: string
  ): { valid: boolean; payload?: any; reason?: string } {
    try {
      const parts = token.split('.');
      if (parts.length !== 2) {
        return { valid: false, reason: 'Invalid token format' };
      }
      
      const [tokenData, signature] = parts;
      if (!tokenData || !signature) {
        return { valid: false, reason: 'Missing token parts' };
      }
      
      // 验证签名
      const expectedSignature = crypto
        .createHmac('sha256', PluginSecurity.PLUGIN_SIGNING_KEY)
        .update(tokenData)
        .digest('hex');
      
      if (!crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'))) {
        return { valid: false, reason: 'Invalid signature' };
      }
      
      // 解析payload
      const payload = JSON.parse(Buffer.from(tokenData, 'base64').toString('utf8'));
      
      // 检查过期时间
      if (Date.now() > payload.expires) {
        return { valid: false, reason: 'Token expired' };
      }
      
      // 检查pluginId和agentId（如果提供）
      if (expectedPluginId && payload.pluginId !== expectedPluginId) {
        return { valid: false, reason: 'Plugin ID mismatch' };
      }
      
      if (expectedAgentId && payload.agentId !== expectedAgentId) {
        return { valid: false, reason: 'Agent ID mismatch' };
      }
      
      return { valid: true, payload };
    } catch (error) {
      return { valid: false, reason: 'Token parsing error' };
    }
  }
  
  /**
   * 生成插件审核标识
   */
  public static generateAuditHash(
    pluginData: Buffer,
    metadata: any,
    auditResult: {
      approved: boolean;
      reviewer: string;
      timestamp: number;
      notes?: string;
    }
  ): string {
    const pluginHash = PluginSecurity.calculatePluginHash(pluginData);
    const auditData = JSON.stringify({
      pluginHash,
      metadata: {
        name: metadata.name,
        version: metadata.version,
        author: metadata.author
      },
      audit: auditResult
    });
    
    return crypto
      .createHmac('sha256', PluginSecurity.PLUGIN_SIGNING_KEY)
      .update(auditData)
      .digest('hex');
  }
  
  /**
   * 验证插件审核状态
   */
  public static verifyAuditHash(
    pluginData: Buffer,
    metadata: any,
    auditResult: any,
    auditHash: string
  ): boolean {
    try {
      const expectedHash = PluginSecurity.generateAuditHash(pluginData, metadata, auditResult);
      return crypto.timingSafeEqual(
        Buffer.from(auditHash, 'hex'),
        Buffer.from(expectedHash, 'hex')
      );
    } catch (error) {
      return false;
    }
  }
}