import crypto from 'crypto';

export class XOREncryption {
  private static readonly DEFAULT_KEY_LENGTH = 32;
  
  /**
   * 生成随机密钥
   */
  public static generateKey(length: number = XOREncryption.DEFAULT_KEY_LENGTH): Buffer {
    return crypto.randomBytes(length);
  }
  
  /**
   * XOR加密/解密
   * @param data 要加密或解密的数据
   * @param key 密钥
   * @returns 加密或解密后的数据
   */
  public static encrypt(data: Buffer, key: Buffer): Buffer {
    const result = Buffer.alloc(data.length);
    const keyLength = key.length;
    
    for (let i = 0; i < data.length; i++) {
      result[i] = data[i]! ^ key[i % keyLength]!;
    }
    
    return result;
  }
  
  /**
   * XOR解密（同加密函数）
   */
  public static decrypt(data: Buffer, key: Buffer): Buffer {
    return XOREncryption.encrypt(data, key);
  }
  
  /**
   * 字符串XOR加密
   */
  public static encryptString(text: string, key: Buffer): string {
    const data = Buffer.from(text, 'utf8');
    const encrypted = XOREncryption.encrypt(data, key);
    return encrypted.toString('base64');
  }
  
  /**
   * 字符串XOR解密
   */
  public static decryptString(encryptedText: string, key: Buffer): string {
    const data = Buffer.from(encryptedText, 'base64');
    const decrypted = XOREncryption.decrypt(data, key);
    return decrypted.toString('utf8');
  }
}

export class KeyExchange {
  /**
   * 生成密钥交换参数
   */
  public static generateKeyExchangeData(): {
    clientRandom: Buffer;
    serverRandom: Buffer;
    sessionKey: Buffer;
  } {
    const clientRandom = crypto.randomBytes(16);
    const serverRandom = crypto.randomBytes(16);
    
    // 简单的密钥派生（实际项目中应使用更安全的方法）
    const combinedData = Buffer.concat([clientRandom, serverRandom]);
    const sessionKey = crypto.createHash('sha256').update(combinedData).digest().slice(0, 32);
    
    return {
      clientRandom,
      serverRandom,
      sessionKey
    };
  }
  
  /**
   * 从客户端和服务器随机数生成会话密钥
   */
  public static deriveSessionKey(clientRandom: Buffer, serverRandom: Buffer): Buffer {
    const combinedData = Buffer.concat([clientRandom, serverRandom]);
    return crypto.createHash('sha256').update(combinedData).digest().slice(0, 32);
  }
}

export class MessageIntegrity {
  /**
   * 计算消息校验和
   */
  public static calculateChecksum(data: string | Buffer): string {
    const input = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;
    return crypto.createHash('sha256').update(input).digest('hex');
  }
  
  /**
   * 验证消息完整性
   */
  public static verifyChecksum(data: string | Buffer, expectedChecksum: string): boolean {
    const actualChecksum = MessageIntegrity.calculateChecksum(data);
    return actualChecksum === expectedChecksum;
  }
  
  /**
   * 生成带时间戳的签名（防重放攻击）
   */
  public static generateTimestampedSignature(data: string, key: Buffer, timestamp?: number): string {
    const ts = timestamp || Date.now();
    const signData = `${data}:${ts}`;
    const hash = crypto.createHmac('sha256', key).update(signData).digest('hex');
    return `${hash}:${ts}`;
  }
  
  /**
   * 验证带时间戳的签名
   */
  public static verifyTimestampedSignature(
    data: string, 
    signature: string, 
    key: Buffer, 
    maxAge: number = 300000 // 5分钟
  ): boolean {
    try {
      const parts = signature.split(':');
      if (parts.length !== 2) {
        return false;
      }
      const [hash, timestampStr] = parts;
      if (!hash || !timestampStr) {
        return false;
      }
      const timestamp = parseInt(timestampStr, 10);
      
      // 检查时间戳有效性
      const now = Date.now();
      if (now - timestamp > maxAge) {
        return false;
      }
      
      // 验证签名
      const expectedSignature = MessageIntegrity.generateTimestampedSignature(data, key, timestamp);
      const [expectedHash] = expectedSignature.split(':');
      
      return hash === expectedHash;
    } catch (error) {
      return false;
    }
  }
}

export class SecureRandom {
  /**
   * 生成安全的随机字符串
   */
  public static generateId(length: number = 16): string {
    return crypto.randomBytes(length).toString('hex');
  }
  
  /**
   * 生成UUID v4
   */
  public static generateUUID(): string {
    return crypto.randomUUID();
  }
  
  /**
   * 生成随机整数
   */
  public static randomInt(min: number, max: number): number {
    return crypto.randomInt(min, max + 1);
  }
}