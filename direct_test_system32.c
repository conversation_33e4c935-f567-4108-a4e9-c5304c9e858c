#include <stdio.h>
#include <windows.h>

int main() {
    printf("=== 直接测试文件管理插件访问System32 ===\n\n");
    
    // 停止Agent进程以释放DLL
    system("taskkill /F /IM agent.exe 2>nul");
    Sleep(1000);
    
    // 加载插件DLL
    HMODULE plugin_handle = LoadLibraryA(".\\agent_full\\plugins\\file_manager.dll");
    if (!plugin_handle) {
        printf("❌ 无法加载插件DLL，错误代码: %lu\n", GetLastError());
        return 1;
    }
    
    // 获取插件函数
    typedef int (*plugin_init_func)(void);
    typedef char* (*plugin_handle_command_func)(const char*);
    typedef int (*plugin_cleanup_func)(void);
    
    plugin_init_func plugin_init = (plugin_init_func)GetProcAddress(plugin_handle, "plugin_init");
    plugin_handle_command_func plugin_handle_command = (plugin_handle_command_func)GetProcAddress(plugin_handle, "plugin_handle_command");
    plugin_cleanup_func plugin_cleanup = (plugin_cleanup_func)GetProcAddress(plugin_handle, "plugin_cleanup");
    
    if (!plugin_init || !plugin_handle_command || !plugin_cleanup) {
        printf("❌ 无法获取插件函数\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    
    // 初始化插件
    printf("1. 初始化插件...\n");
    if (plugin_init() != 0) {
        printf("❌ 插件初始化失败\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    printf("✅ 插件初始化成功\n\n");
    
    // 测试System32目录访问
    printf("2. 测试访问 C:\\\\Windows\\\\System32 目录：\n");
    const char* system32_command = "{\"command\":\"list_directory\",\"path\":\"C:\\\\Windows\\\\System32\"}";
    printf("发送命令: %s\n\n", system32_command);
    
    char* result = plugin_handle_command(system32_command);
    if (result) {
        printf("✅ 成功访问System32目录！\n");
        printf("返回结果前500字符:\n");
        printf("%.500s", result);
        if (strlen(result) > 500) {
            printf("...[结果太长，已截断]\n");
        }
        printf("\n\n");
        free(result);
    } else {
        printf("❌ 访问System32失败\n\n");
    }
    
    // 测试其他敏感目录
    printf("3. 测试访问 C:\\\\Windows\\\\SysWOW64 目录：\n");
    const char* syswow64_command = "{\"command\":\"list_directory\",\"path\":\"C:\\\\Windows\\\\SysWOW64\"}";
    
    result = plugin_handle_command(syswow64_command);
    if (result) {
        printf("✅ 成功访问SysWOW64目录！\n");
        printf("返回结果前200字符: %.200s...\n\n", result);
        free(result);
    } else {
        printf("❌ 访问SysWOW64失败\n\n");
    }
    
    // 测试获取Windows目录信息
    printf("4. 测试获取Windows目录信息：\n");
    const char* windows_info_command = "{\"command\":\"get_file_info\",\"path\":\"C:\\\\Windows\"}";
    
    result = plugin_handle_command(windows_info_command);
    if (result) {
        printf("✅ 成功获取Windows目录信息！\n");
        printf("结果: %s\n\n", result);
        free(result);
    } else {
        printf("❌ 获取Windows目录信息失败\n\n");
    }
    
    // 清理
    printf("5. 清理插件...\n");
    plugin_cleanup();
    FreeLibrary(plugin_handle);
    
    printf("✅ 测试完成！安全限制已完全移除，可以访问所有系统目录！\n");
    return 0;
}