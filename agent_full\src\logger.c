#include "logger.h"
#include <stdarg.h>

static LogLevel g_log_level = LOG_INFO;
static bool g_logger_initialized = false;

AgentResult logger_init(LogLevel level)
{
    g_log_level = level;
    g_logger_initialized = true;
    
    LOG_INFO_MSG("Logger initialized with level: %d", level);
    return AGENT_SUCCESS;
}

void logger_cleanup(void)
{
    if (g_logger_initialized) {
        LOG_INFO_MSG("Logger cleanup");
        g_logger_initialized = false;
    }
}

void logger_log(LogLevel level, const char* format, ...)
{
    if (!g_logger_initialized || level < g_log_level) {
        return;
    }
    
    const char* level_names[] = {"DEBUG", "INFO", "WARN", "ERROR"};
    
    char timestamp[32];
    format_timestamp(timestamp, sizeof(timestamp));
    
    printf("[%s] %s: ", timestamp, level_names[level]);
    
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    
    printf("\n");
    fflush(stdout);
}