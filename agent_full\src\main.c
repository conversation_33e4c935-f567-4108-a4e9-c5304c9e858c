#include "agent.h"
#include "websocket_client.h"
#include "plugin_manager.h"
#include "message_handler.h"
#include "logger.h"
#include "utils.h"

#include <signal.h>

static volatile bool g_running = true;
static AgentContext* g_ctx = NULL;

void signal_handler(int sig)
{
    LOG_INFO_MSG("Received signal %d, shutting down...", sig);
    g_running = false;
    if (g_ctx) {
        g_ctx->state = AGENT_STATE_SHUTDOWN;
    }
}

void setup_signal_handlers(void)
{
#ifdef _WIN32
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
#else
    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
#endif
}

AgentResult initialize_agent(AgentContext* ctx)
{
    LOG_INFO_MSG("Initializing agent...");
    
    // Initialize context
    memset(ctx, 0, sizeof(AgentContext));
    ctx->websocket = INVALID_SOCKET;
    ctx->state = AGENT_STATE_DISCONNECTED;
    ctx->stats.start_time = get_timestamp_ms();
    
    // Load default configuration
    strcpy(ctx->config.agent_id, "agent_full_001");
    strcpy(ctx->config.server_host, "localhost");
    ctx->config.server_port = 8080;
    strcpy(ctx->config.plugin_directory, "C:\\Users\\<USER>\\Desktop\\win\\agent_full\\plugins");
    ctx->config.heartbeat_interval = 30000; // 30 seconds
    ctx->config.reconnect_interval = 5000;  // 5 seconds
    ctx->config.max_reconnect_attempts = 10;
    
    // Initialize components
    AgentResult result;
    
    result = logger_init(LOG_INFO);
    if (result != AGENT_SUCCESS) {
        printf("Failed to initialize logger\n");
        return result;
    }
    
    result = ws_client_init();
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize WebSocket client");
        return result;
    }
    
    result = plugin_manager_init(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize plugin manager");
        return result;
    }
    
    result = message_handler_init(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize message handler");
        return result;
    }
    
    LOG_INFO_MSG("Agent initialized successfully");
    return AGENT_SUCCESS;
}

void cleanup_agent(AgentContext* ctx)
{
    if (!ctx) return;
    
    LOG_INFO_MSG("Cleaning up agent...");
    
    // Disconnect from server
    if (ws_client_is_connected(ctx)) {
        ws_client_disconnect(ctx);
    }
    
    // Cleanup components
    message_handler_cleanup(ctx);
    plugin_manager_cleanup(ctx);
    ws_client_cleanup();
    logger_cleanup();
    
    LOG_INFO_MSG("Agent cleanup completed");
}

AgentResult main_loop(AgentContext* ctx)
{
    LOG_INFO_MSG("Starting main loop...");
    
    uint64_t last_heartbeat = 0;
    uint64_t last_plugin_scan = 0;
    uint64_t last_plugin_execution = 0;
    
    while (g_running && ctx->state != AGENT_STATE_SHUTDOWN) {
        uint64_t now = get_timestamp_ms();
        
        // Try to connect if not connected
        if (!ws_client_is_connected(ctx)) {
            if (ctx->reconnect_count < ctx->config.max_reconnect_attempts) {
                LOG_INFO_MSG("Attempting to connect to server... (attempt %d/%d)",
                           ctx->reconnect_count + 1, ctx->config.max_reconnect_attempts);
                
                AgentResult result = ws_client_connect(ctx);
                if (result == AGENT_SUCCESS) {
                    LOG_INFO_MSG("Connected to server successfully");
                    // Send initial system info
                    ws_client_send_system_info(ctx);
                } else {
                    ctx->reconnect_count++;
                    LOG_WARN_MSG("Connection failed, will retry in %d ms", 
                               ctx->config.reconnect_interval);
                    
#ifdef _WIN32
                    Sleep(ctx->config.reconnect_interval);
#else
                    usleep(ctx->config.reconnect_interval * 1000);
#endif
                    continue;
                }
            } else {
                LOG_ERROR_MSG("Max reconnection attempts reached, shutting down");
                break;
            }
        }
        
        // Send heartbeat
        if (now - last_heartbeat >= ctx->config.heartbeat_interval) {
            ws_client_send_heartbeat(ctx);
            last_heartbeat = now;
        }
        
        // Scan for new plugins every 60 seconds
        if (now - last_plugin_scan >= 60000) {
            plugin_manager_scan_directory(ctx);
            last_plugin_scan = now;
        }
        
        // Execute plugins every 10 seconds
        if (now - last_plugin_execution >= 10000) {
            plugin_manager_execute_plugins(ctx);
            last_plugin_execution = now;
        }
        
        // Check for incoming messages
        char message_buffer[2048];
        size_t received_size;
        
        AgentResult result = ws_client_receive_message(ctx, message_buffer, 
                                                     sizeof(message_buffer), &received_size);
        
        if (result == AGENT_SUCCESS && received_size > 0) {
            message_handler_process(ctx, message_buffer);
        } else if (result == AGENT_ERROR_NETWORK) {
            LOG_WARN_MSG("Network error, connection lost");
            ctx->state = AGENT_STATE_DISCONNECTED;
            ctx->reconnect_count = 0;
        }
        
        // Small delay to prevent busy waiting
#ifdef _WIN32
        Sleep(100);
#else
        usleep(100000);
#endif
    }
    
    LOG_INFO_MSG("Main loop ended");
    return AGENT_SUCCESS;
}

int main(int argc, char* argv[])
{
    printf("Agent Full v1.0 - Distributed Monitoring System\n");
    printf("Starting agent...\n");
    
    // Setup signal handlers
    setup_signal_handlers();
    
    AgentContext ctx;
    g_ctx = &ctx;
    
    // Initialize agent
    AgentResult result = initialize_agent(&ctx);
    if (result != AGENT_SUCCESS) {
        printf("Failed to initialize agent: %d\n", result);
        return 1;
    }
    
    // Run main loop
    result = main_loop(&ctx);
    
    // Cleanup
    cleanup_agent(&ctx);
    
    printf("Agent terminated with code: %d\n", result);
    return (result == AGENT_SUCCESS) ? 0 : 1;
}