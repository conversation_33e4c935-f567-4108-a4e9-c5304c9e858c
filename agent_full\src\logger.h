#ifndef LOGGER_H
#define LOGGER_H

#include "agent.h"

// Logger functions
AgentResult logger_init(LogLevel level);
void logger_cleanup(void);
void logger_log(LogLevel level, const char* format, ...);

// Convenience macros
#define LOG_DEBUG_MSG(msg, ...) logger_log(LOG_DEBUG, msg, ##__VA_ARGS__)
#define LOG_INFO_MSG(msg, ...) logger_log(LOG_INFO, msg, ##__VA_ARGS__)
#define LOG_WARN_MSG(msg, ...) logger_log(LOG_WARN, msg, ##__VA_ARGS__)
#define LOG_ERROR_MSG(msg, ...) logger_log(LOG_ERROR, msg, ##__VA_ARGS__)

#endif // LOGGER_H