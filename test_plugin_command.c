#include <stdio.h>
#include <windows.h>

int main() {
    printf("Testing file manager plugin command handling...\n");
    
    // 加载插件DLL
    HMODULE plugin_handle = LoadLibraryA(".\\agent_full\\plugins\\file_manager.dll");
    if (!plugin_handle) {
        printf("❌ Failed to load plugin DLL, error: %lu\n", GetLastError());
        return 1;
    }
    
    // 获取插件函数
    typedef int (*plugin_init_func)(void);
    typedef char* (*plugin_handle_command_func)(const char*);
    
    plugin_init_func plugin_init = (plugin_init_func)GetProcAddress(plugin_handle, "plugin_init");
    plugin_handle_command_func plugin_handle_command = (plugin_handle_command_func)GetProcAddress(plugin_handle, "plugin_handle_command");
    
    if (!plugin_init || !plugin_handle_command) {
        printf("❌ Failed to get plugin functions\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    
    // 初始化插件
    printf("Initializing plugin...\n");
    if (plugin_init() != 0) {
        printf("❌ Plugin initialization failed\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    printf("✅ Plugin initialized successfully\n");
    
    // 测试命令
    const char* test_commands[] = {
        "{\"command\":\"get_drives\"}",
        "{\"command\":\"list_directory\",\"path\":\"C:\\\\Windows\\\\System32\"}",
        "{\"command\":\"get_file_info\",\"path\":\"C:\\\\Windows\"}"
    };
    
    const char* command_names[] = {
        "Get drives",
        "List System32 directory", 
        "Get Windows directory info"
    };
    
    int num_tests = sizeof(test_commands) / sizeof(test_commands[0]);
    
    for (int i = 0; i < num_tests; i++) {
        printf("\n%d. Testing: %s\n", i + 1, command_names[i]);
        printf("Command: %s\n", test_commands[i]);
        
        char* result = plugin_handle_command(test_commands[i]);
        if (result) {
            printf("✅ Result: %.300s", result);
            if (strlen(result) > 300) {
                printf("...[truncated]");
            }
            printf("\n");
            free(result);
        } else {
            printf("❌ Command execution failed or returned NULL\n");
        }
    }
    
    FreeLibrary(plugin_handle);
    printf("\n✅ Plugin test completed!\n");
    return 0;
}