cmake_minimum_required(VERSION 3.16)
project(FileManagerPlugin VERSION 1.0.0 LANGUAGES C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler options
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(_WINSOCK_DEPRECATED_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -fPIC)
endif()

# Include directories
include_directories(include)

# Source files
set(PLUGIN_SOURCES
    src/file_manager.c
)

# Header files
set(PLUGIN_HEADERS
    include/file_manager.h
)

# Create shared library (DLL on Windows)
add_library(file_manager SHARED ${PLUGIN_SOURCES} ${PLUGIN_HEADERS})

# Windows specific settings
if(WIN32)
    set_target_properties(file_manager PROPERTIES
        PREFIX ""
        OUTPUT_NAME "file_manager"
        SUFFIX ".dll"
    )
    # Link Windows specific libraries
    target_link_libraries(file_manager shlwapi)
endif()

# Linux specific settings
if(UNIX AND NOT APPLE)
    set_target_properties(file_manager PROPERTIES
        PREFIX ""
        OUTPUT_NAME "file_manager"
        SUFFIX ".so"
    )
endif()

# macOS specific settings
if(APPLE)
    set_target_properties(file_manager PROPERTIES
        PREFIX ""
        OUTPUT_NAME "file_manager"
        SUFFIX ".dylib"
    )
endif()

# Install
install(TARGETS file_manager
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)