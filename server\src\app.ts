import express from 'express';
import http from 'http';
import { WebSocketServer } from 'ws';
import cors from 'cors';
import path from 'path';
import { ConnectionManager } from '@services/ConnectionManager';
import { WebSocketConfigManager } from '@config/websocket';
import logger from '@utils/logger';

export async function createApp() {
  const app = express();
  const server = http.createServer(app);
  
  // 基础中间件配置
  app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
  }));
  
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  
  // 静态文件服务
  app.use(express.static(path.join(process.cwd(), 'public')));
  
  // 日志中间件
  app.use((req, res, next) => {
    logger.info(`${req.method} ${req.path} - ${req.ip}`);
    next();
  });
  
  // 健康检查端点
  app.get('/health', (req, res) => {
    const connectionManager = ConnectionManager.getInstance();
    const stats = connectionManager.getStats();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      stats,
      uptime: process.uptime(),
      memory: process.memoryUsage()
    });
  });
  
  // API路由
  app.get('/api/agents', (req, res) => {
    const connectionManager = ConnectionManager.getInstance();
    const connections = connectionManager.getAllConnections();
    
    res.json({
      total: connections.length,
      agents: connections.map(conn => ({
        id: conn.id,
        status: conn.status,
        connectTime: conn.connectTime,
        lastHeartbeat: conn.lastHeartbeat,
        systemInfo: conn.systemInfo,
        loadedPlugins: conn.loadedPlugins,
        remoteAddress: conn.remoteAddress
      }))
    });
  });
  
  app.get('/api/agents/:agentId', (req, res) => {
    const { agentId } = req.params;
    const connectionManager = ConnectionManager.getInstance();
    const connection = connectionManager.getConnection(agentId);
    
    if (!connection) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    
    return res.json({
      id: connection.id,
      status: connection.status,
      connectTime: connection.connectTime,
      lastHeartbeat: connection.lastHeartbeat,
      systemInfo: connection.systemInfo,
      loadedPlugins: connection.loadedPlugins,
      remoteAddress: connection.remoteAddress
    });
  });
  
  app.get('/api/stats', (req, res) => {
    const connectionManager = ConnectionManager.getInstance();
    const stats = connectionManager.getStats();
    
    res.json({
      ...stats,
      serverUptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date().toISOString()
    });
  });
  
  // 错误处理中间件
  app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
    logger.error('HTTP请求错误:', err);
    res.status(500).json({
      error: 'Internal Server Error',
      message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
  });
  
  // 404处理
  app.use((req, res) => {
    res.status(404).json({
      error: 'Not Found',
      path: req.path
    });
  });
  
  // 创建WebSocket服务器
  const wsConfig = WebSocketConfigManager.getInstance().getConfig();
  const wss = new WebSocketServer({ 
    port: wsConfig.port,
    maxPayload: wsConfig.messageMaxSize
  });
  
  const connectionManager = ConnectionManager.getInstance();
  
  // WebSocket连接处理
  wss.on('connection', (ws, req) => {
    const remoteAddress = req.socket.remoteAddress || 'unknown';
    const agentId = connectionManager.addConnection(ws, remoteAddress);
    
    if (!agentId) {
      logger.warn(`拒绝连接：已达到最大连接数限制`);
      ws.close(1013, 'Server overloaded');
      return;
    }
    
    logger.info(`新Agent连接: ${agentId} from ${remoteAddress}`);
  });
  
  // WebSocket服务器事件
  wss.on('error', (error) => {
    logger.error('WebSocket服务器错误:', error);
  });
  
  wss.on('listening', () => {
    logger.info(`WebSocket服务器监听端口: ${wsConfig.port}`);
  });
  
  // 设置定期ping
  setInterval(() => {
    connectionManager.pingAllConnections();
  }, wsConfig.pingInterval);
  
  // 连接管理器事件监听
  connectionManager.on('agent-connected', (agentId, connection) => {
    logger.info(`Agent已连接: ${agentId}`);
  });
  
  connectionManager.on('agent-disconnected', (agentId, reason) => {
    logger.info(`Agent已断开: ${agentId}, 原因: ${reason}`);
  });
  
  connectionManager.on('agent-message', (agentId, message) => {
    logger.debug(`收到Agent消息: ${agentId}, 类型: ${message.type}`);
  });
  
  connectionManager.on('agent-error', (agentId, error) => {
    logger.error(`Agent错误 ${agentId}:`, error);
  });
  
  connectionManager.on('heartbeat-timeout', (agentId) => {
    logger.warn(`Agent心跳超时: ${agentId}`);
  });
  
  connectionManager.on('max-connections-reached', () => {
    logger.warn('已达到最大连接数限制');
  });
  
  return { app, server, wss };
}