#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>

// 导入插件函数
typedef int (*plugin_init_func)(void);
typedef char* (*plugin_handle_command_func)(const char*);
typedef int (*plugin_cleanup_func)(void);

int main() {
    printf("=== 文件管理插件直接测试 ===\n\n");
    
    // 加载插件DLL
    HMODULE plugin_handle = LoadLibraryA("C:\\Users\\<USER>\\Desktop\\win\\agent_full\\plugins\\file_manager.dll");
    if (!plugin_handle) {
        printf("❌ 无法加载插件DLL\n");
        return 1;
    }
    
    // 获取插件函数
    plugin_init_func plugin_init = (plugin_init_func)GetProcAddress(plugin_handle, "plugin_init");
    plugin_handle_command_func plugin_handle_command = (plugin_handle_command_func)GetProcAddress(plugin_handle, "plugin_handle_command");
    plugin_cleanup_func plugin_cleanup = (plugin_cleanup_func)GetProcAddress(plugin_handle, "plugin_cleanup");
    
    if (!plugin_init || !plugin_handle_command || !plugin_cleanup) {
        printf("❌ 无法获取插件函数\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    
    // 初始化插件
    printf("1. 初始化插件...\n");
    if (plugin_init() != 0) {
        printf("❌ 插件初始化失败\n");
        FreeLibrary(plugin_handle);
        return 1;
    }
    printf("✅ 插件初始化成功\n\n");
    
    // 测试命令
    const char* test_commands[] = {
        "{\"command\":\"get_drives\"}",
        "{\"command\":\"list_directory\",\"path\":\"C:\\\\\"}",
        "{\"command\":\"get_file_info\",\"path\":\"C:\\\\Windows\"}",
        "{\"command\":\"create_directory\",\"path\":\"C:\\\\TestFileManager\"}"
    };
    
    const char* command_names[] = {
        "获取磁盘驱动器列表",
        "列出C盘根目录",
        "获取Windows目录信息", 
        "创建测试目录"
    };
    
    int num_tests = sizeof(test_commands) / sizeof(test_commands[0]);
    
    for (int i = 0; i < num_tests; i++) {
        printf("%d. %s\n", i + 2, command_names[i]);
        printf("发送命令: %s\n", test_commands[i]);
        
        char* result = plugin_handle_command(test_commands[i]);
        if (result) {
            printf("返回结果: %.200s", result);
            if (strlen(result) > 200) {
                printf("...[结果太长，已截断]");
            }
            printf("\n");
            free(result);
        } else {
            printf("❌ 命令执行失败\n");
        }
        printf("\n");
    }
    
    // 清理插件
    printf("清理插件...\n");
    plugin_cleanup();
    FreeLibrary(plugin_handle);
    
    printf("✅ 测试完成！\n");
    return 0;
}