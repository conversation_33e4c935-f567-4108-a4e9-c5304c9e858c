import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { PluginInfo, PluginCategory, PluginDistribution, PluginExecutionResult } from '../types/Plugin';
import { MessageType, PluginDownloadMessage } from '../types/Message';
import { ConnectionManager } from './ConnectionManager';
import { SecureRandom, PluginSecurity } from '@utils/crypto';
import logger from '@utils/logger';

export interface PluginUploadData {
  name: string;
  version: string;
  description: string;
  author: string;
  category: PluginCategory;
  targetSystems: string[];
  dependencies: string[];
  fileBuffer: Buffer;
  fileName: string;
}

export interface DistributionResult {
  success: boolean;
  agentId: string;
  distributionId?: string;
  error?: string;
}

export interface ExecutionResultsQuery {
  pluginId: string;
  agentId?: string | undefined;
  startDate?: Date | undefined;
  endDate?: Date | undefined;
  page: number;
  limit: number;
}

export interface PaginatedResults<T> {
  records: T[];
  total: number;
}

export class PluginService {
  private static instance: PluginService;
  private pluginsDirectory: string;
  private plugins: Map<string, PluginInfo> = new Map();
  private distributions: Map<string, PluginDistribution> = new Map();
  private executionResults: PluginExecutionResult[] = [];
  private connectionManager: ConnectionManager;

  private constructor() {
    this.pluginsDirectory = path.join(process.cwd(), 'uploads', 'plugins');
    this.connectionManager = ConnectionManager.getInstance();
    this.ensurePluginsDirectory();
    this.loadExistingPlugins();
  }

  public static getInstance(): PluginService {
    if (!PluginService.instance) {
      PluginService.instance = new PluginService();
    }
    return PluginService.instance;
  }

  /**
   * 确保插件目录存在
   */
  private ensurePluginsDirectory(): void {
    if (!fs.existsSync(this.pluginsDirectory)) {
      fs.mkdirSync(this.pluginsDirectory, { recursive: true });
      logger.info(`Created plugins directory: ${this.pluginsDirectory}`);
    }
  }

  /**
   * 加载现有插件
   */
  private loadExistingPlugins(): void {
    try {
      const metadataPath = path.join(this.pluginsDirectory, 'metadata.json');
      if (fs.existsSync(metadataPath)) {
        const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        metadata.plugins?.forEach((plugin: PluginInfo) => {
          this.plugins.set(plugin.id, plugin);
        });
        logger.info(`Loaded ${this.plugins.size} existing plugins`);
      }
    } catch (error) {
      logger.logError(error as Error, 'Failed to load existing plugins');
    }
  }

  /**
   * 保存插件元数据
   */
  private saveMetadata(): void {
    try {
      const metadataPath = path.join(this.pluginsDirectory, 'metadata.json');
      const metadata = {
        plugins: Array.from(this.plugins.values()),
        lastUpdated: new Date().toISOString()
      };
      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      logger.logError(error as Error, 'Failed to save plugin metadata');
    }
  }

  /**
   * 计算文件校验和
   */
  private calculateChecksum(buffer: Buffer): string {
    return PluginSecurity.calculatePluginHash(buffer);
  }

  /**
   * 上传插件
   */
  public async uploadPlugin(data: PluginUploadData): Promise<PluginInfo> {
    const pluginId = SecureRandom.generateUUID();
    const checksum = this.calculateChecksum(data.fileBuffer);
    const fileName = `${pluginId}_${data.fileName}`;
    const filePath = path.join(this.pluginsDirectory, fileName);

    // 生成插件签名
    const signature = PluginSecurity.signPlugin(data.fileBuffer, {
      name: data.name,
      version: data.version,
      author: data.author
    });

    // 写入文件
    fs.writeFileSync(filePath, data.fileBuffer);

    const pluginInfo: PluginInfo = {
      id: pluginId,
      name: data.name,
      version: data.version,
      description: data.description,
      author: data.author,
      filePath,
      fileSize: data.fileBuffer.length,
      checksum,
      signature,
      uploadTime: new Date(),
      dependencies: data.dependencies,
      targetSystems: data.targetSystems,
      category: data.category,
      enabled: true
    };

    this.plugins.set(pluginId, pluginInfo);
    this.saveMetadata();

    logger.info(`Plugin uploaded: ${data.name} (${pluginId})`);
    return pluginInfo;
  }

  /**
   * 获取所有插件
   */
  public async getAllPlugins(): Promise<PluginInfo[]> {
    return Array.from(this.plugins.values());
  }

  /**
   * 根据ID获取插件
   */
  public async getPluginById(id: string): Promise<PluginInfo | null> {
    return this.plugins.get(id) || null;
  }

  /**
   * 删除插件
   */
  public async deletePlugin(id: string): Promise<boolean> {
    const plugin = this.plugins.get(id);
    if (!plugin) {
      return false;
    }

    try {
      // 删除文件
      if (fs.existsSync(plugin.filePath)) {
        fs.unlinkSync(plugin.filePath);
      }

      // 从内存中移除
      this.plugins.delete(id);
      this.saveMetadata();

      logger.info(`Plugin deleted: ${plugin.name} (${id})`);
      return true;
    } catch (error) {
      logger.logError(error as Error, `Failed to delete plugin ${id}`);
      return false;
    }
  }

  /**
   * 向指定Agent分发插件
   */
  public async distributePluginToAgent(
    pluginId: string, 
    agentId: string, 
    autoExecute: boolean = false, 
    priority: number = 1
  ): Promise<DistributionResult> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return {
        success: false,
        agentId,
        error: 'Plugin not found'
      };
    }

    try {
      // 读取插件文件
      const pluginData = fs.readFileSync(plugin.filePath);
      
      // 验证插件完整性
      if (!PluginSecurity.validatePluginIntegrity(pluginData, plugin.checksum)) {
        return {
          success: false,
          agentId,
          error: 'Plugin integrity check failed'
        };
      }
      
      // 生成分发令牌
      const distributionToken = PluginSecurity.generateDistributionToken(pluginId, agentId);
      const base64Data = pluginData.toString('base64');

      // 创建分发记录
      const distributionId = SecureRandom.generateUUID();
      const distribution: PluginDistribution = {
        pluginId,
        targetAgents: [agentId],
        distributionTime: new Date(),
        autoExecute,
        priority
      };
      this.distributions.set(distributionId, distribution);

      // 发送到Agent
      const message: PluginDownloadMessage = {
        type: MessageType.PLUGIN_DOWNLOAD,
        timestamp: Date.now(),
        agentId,
        messageId: `download-${pluginId}-${Date.now()}`,
        data: {
          pluginId,
          pluginName: plugin.name,
          pluginData: base64Data,
          checksum: plugin.checksum,
          autoExecute,
          priority
        }
      };

      const success = await this.connectionManager.sendToAgent(agentId, message);
      
      if (success) {
        logger.info(`Plugin ${pluginId} distributed to agent ${agentId}`);
        return {
          success: true,
          agentId,
          distributionId
        };
      } else {
        return {
          success: false,
          agentId,
          error: 'Failed to send to agent'
        };
      }

    } catch (error) {
      logger.logError(error as Error, `Plugin distribution failed: ${pluginId} to ${agentId}`);
      return {
        success: false,
        agentId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 向多个Agent批量分发插件
   */
  public async distributeToBatchAgents(
    pluginId: string,
    agentIds: string[],
    autoExecute: boolean = false,
    priority: number = 1
  ): Promise<DistributionResult[]> {
    const results: DistributionResult[] = [];
    
    for (const agentId of agentIds) {
      const result = await this.distributePluginToAgent(pluginId, agentId, autoExecute, priority);
      results.push(result);
    }

    return results;
  }

  /**
   * 获取分发历史
   */
  public async getDistributionHistory(
    pluginId?: string,
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedResults<PluginDistribution>> {
    let distributions = Array.from(this.distributions.values());
    
    if (pluginId) {
      distributions = distributions.filter(d => d.pluginId === pluginId);
    }

    // 按时间倒序排列
    distributions.sort((a, b) => b.distributionTime.getTime() - a.distributionTime.getTime());

    const total = distributions.length;
    const start = (page - 1) * limit;
    const records = distributions.slice(start, start + limit);

    return { records, total };
  }

  /**
   * 记录插件执行结果
   */
  public recordExecutionResult(result: PluginExecutionResult): void {
    this.executionResults.push(result);
    
    // 保持最近1000条记录
    if (this.executionResults.length > 1000) {
      this.executionResults = this.executionResults.slice(-1000);
    }
    
    logger.debug(`Recorded plugin execution result: ${result.pluginId} on ${result.agentId}`);
  }

  /**
   * 获取执行结果
   */
  public async getExecutionResults(query: ExecutionResultsQuery): Promise<PaginatedResults<PluginExecutionResult>> {
    let results = this.executionResults.filter(r => r.pluginId === query.pluginId);

    if (query.agentId) {
      results = results.filter(r => r.agentId === query.agentId);
    }

    if (query.startDate) {
      results = results.filter(r => r.timestamp >= query.startDate!);
    }

    if (query.endDate) {
      results = results.filter(r => r.timestamp <= query.endDate!);
    }

    // 按时间倒序排列
    results.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    const total = results.length;
    const start = (query.page - 1) * query.limit;
    const records = results.slice(start, start + query.limit);

    return { records, total };
  }

  /**
   * 获取插件统计信息
   */
  public async getPluginStats(pluginId: string): Promise<any> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return null;
    }

    const distributions = Array.from(this.distributions.values())
      .filter(d => d.pluginId === pluginId);
    
    const executions = this.executionResults.filter(r => r.pluginId === pluginId);
    const successfulExecutions = executions.filter(r => r.success);

    return {
      plugin,
      stats: {
        totalDistributions: distributions.length,
        totalExecutions: executions.length,
        successfulExecutions: successfulExecutions.length,
        failureRate: executions.length > 0 
          ? ((executions.length - successfulExecutions.length) / executions.length * 100).toFixed(2) + '%'
          : '0%',
        lastDistribution: distributions.length > 0 
          ? Math.max(...distributions.map(d => d.distributionTime.getTime()))
          : null,
        lastExecution: executions.length > 0
          ? Math.max(...executions.map(e => e.timestamp.getTime()))
          : null
      }
    };
  }

  /**
   * 启用/禁用插件
   */
  public async togglePlugin(id: string, enabled: boolean): Promise<boolean> {
    const plugin = this.plugins.get(id);
    if (!plugin) {
      return false;
    }

    plugin.enabled = enabled;
    this.saveMetadata();
    
    logger.info(`Plugin ${plugin.name} ${enabled ? 'enabled' : 'disabled'}`);
    return true;
  }

  /**
   * 获取在线Agent数量
   */
  public getOnlineAgentsCount(): number {
    return this.connectionManager.getHealthyConnections().length;
  }

  /**
   * 获取系统概览
   */
  public async getSystemOverview(): Promise<any> {
    const allPlugins = await this.getAllPlugins();
    const enabledPlugins = allPlugins.filter(p => p.enabled);
    const totalDistributions = this.distributions.size;
    const totalExecutions = this.executionResults.length;
    const onlineAgents = this.getOnlineAgentsCount();

    const recentExecutions = this.executionResults
      .filter(r => Date.now() - r.timestamp.getTime() < 24 * 60 * 60 * 1000) // 最近24小时
      .length;

    return {
      plugins: {
        total: allPlugins.length,
        enabled: enabledPlugins.length,
        disabled: allPlugins.length - enabledPlugins.length
      },
      distributions: {
        total: totalDistributions,
        recent: Array.from(this.distributions.values())
          .filter(d => Date.now() - d.distributionTime.getTime() < 24 * 60 * 60 * 1000)
          .length
      },
      executions: {
        total: totalExecutions,
        recent: recentExecutions,
        successRate: totalExecutions > 0 
          ? ((this.executionResults.filter(r => r.success).length / totalExecutions) * 100).toFixed(2) + '%'
          : 'N/A'
      },
      agents: {
        online: onlineAgents,
        total: this.connectionManager.getAllConnections().length
      }
    };
  }

  /**
   * 审核插件
   */
  public async auditPlugin(
    pluginId: string,
    auditResult: {
      approved: boolean;
      reviewer: string;
      notes?: string;
    }
  ): Promise<boolean> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return false;
    }

    try {
      // 读取插件文件进行审核
      const pluginData = fs.readFileSync(plugin.filePath);
      
      const auditData: any = {
        approved: auditResult.approved,
        reviewer: auditResult.reviewer,
        timestamp: Date.now()
      };
      
      if (auditResult.notes) {
        auditData.notes = auditResult.notes;
      }

      // 生成审核哈希
      const auditHash = PluginSecurity.generateAuditHash(
        pluginData,
        {
          name: plugin.name,
          version: plugin.version,
          author: plugin.author
        },
        auditData
      );

      // 更新插件审核状态
      plugin.auditStatus = {
        approved: auditData.approved,
        reviewer: auditData.reviewer,
        timestamp: auditData.timestamp,
        auditHash
      };
      
      if (auditData.notes) {
        plugin.auditStatus.notes = auditData.notes;
      }

      // 如果审核不通过，禁用插件
      if (!auditResult.approved) {
        plugin.enabled = false;
      }

      this.saveMetadata();
      logger.info(`Plugin ${pluginId} audit completed: ${auditResult.approved ? 'approved' : 'rejected'} by ${auditResult.reviewer}`);
      
      return true;
    } catch (error) {
      logger.logError(error as Error, `Plugin audit failed for ${pluginId}`);
      return false;
    }
  }

  /**
   * 验证插件签名
   */
  public async verifyPluginSignature(pluginId: string): Promise<boolean> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return false;
    }

    try {
      const pluginData = fs.readFileSync(plugin.filePath);
      return PluginSecurity.verifyPluginSignature(
        pluginData,
        {
          name: plugin.name,
          version: plugin.version,
          author: plugin.author
        },
        plugin.signature
      );
    } catch (error) {
      logger.logError(error as Error, `Plugin signature verification failed for ${pluginId}`);
      return false;
    }
  }

  /**
   * 检查插件是否经过审核且被批准
   */
  public isPluginApproved(pluginId: string): boolean {
    const plugin = this.plugins.get(pluginId);
    if (!plugin || !plugin.auditStatus) {
      return false;
    }

    return plugin.auditStatus.approved && plugin.enabled;
  }

  /**
   * 获取插件安全状态
   */
  public async getPluginSecurityStatus(pluginId: string): Promise<any> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return null;
    }

    const signatureValid = await this.verifyPluginSignature(pluginId);
    const integrityValid = await this.checkPluginIntegrity(pluginId);
    const approved = this.isPluginApproved(pluginId);

    return {
      pluginId,
      name: plugin.name,
      version: plugin.version,
      security: {
        signatureValid,
        integrityValid,
        approved,
        auditStatus: plugin.auditStatus,
        uploadTime: plugin.uploadTime
      }
    };
  }

  /**
   * 检查插件完整性
   */
  private async checkPluginIntegrity(pluginId: string): Promise<boolean> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return false;
    }

    try {
      const pluginData = fs.readFileSync(plugin.filePath);
      return PluginSecurity.validatePluginIntegrity(pluginData, plugin.checksum);
    } catch (error) {
      return false;
    }
  }
}