#include "message_handler.h"
#include "websocket_client.h"
#include "logger.h"
#include "utils.h"
#include "plugin_manager.h"

AgentResult message_handler_init(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_INFO_MSG("Message handler initialized");
    return AGENT_SUCCESS;
}

void message_handler_cleanup(AgentContext* ctx)
{
    if (!ctx) {
        return;
    }
    
    LOG_INFO_MSG("Message handler cleanup completed");
}

AgentResult message_handler_process(AgentContext* ctx, const char* message)
{
    if (!ctx || !message) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_DEBUG_MSG("Processing message: %s", message);
    
    MessageType msg_type = parse_message_type(message);
    LOG_DEBUG_MSG("Parsed message type: %d", msg_type);
    
    switch (msg_type) {
        case MSG_TYPE_HEARTBEAT:
            LOG_DEBUG_MSG("Received heartbeat request");
            return ws_client_send_heartbeat(ctx);
            
        case MSG_TYPE_SYSTEM_INFO:
            LOG_DEBUG_MSG("Received system info request");
            return ws_client_send_system_info(ctx);
            
        case MSG_TYPE_PLUGIN_DATA:
            LOG_DEBUG_MSG("Received plugin data request");
            {
                char* plugin_data = plugin_collect_data(ctx);
                if (plugin_data) {
                    char* response = create_json_message("plugin_data", ctx->config.agent_id, plugin_data);
                    if (response) {
                        AgentResult result = ws_client_send_message(ctx, response);
                        free_json_message(response);
                        free(plugin_data);
                        return result;
                    }
                    free(plugin_data);
                }
            }
            break;
            
        case MSG_TYPE_STATUS:
            LOG_DEBUG_MSG("Received status request");
            return message_handler_send_status(ctx);
            
        case MSG_TYPE_COMMAND:
            {
                char* command = extract_json_field(message, "command");
                char* params = extract_json_field(message, "params");
                
                AgentResult result = message_handler_handle_command(ctx, command, params);
                
                if (command) free(command);
                if (params) free(params);
                
                return result;
            }
            
        case MSG_TYPE_PLUGIN_DOWNLOAD:
            LOG_DEBUG_MSG("Received plugin download request");
            return message_handler_handle_plugin_download(ctx, message);
            
        case MSG_TYPE_PLUGIN_EXECUTE:
            LOG_DEBUG_MSG("Received plugin execute request");
            return message_handler_handle_plugin_execute(ctx, message);
            
        case MSG_TYPE_PLUGIN_COMMAND:
            LOG_DEBUG_MSG("Received plugin command request");
            return message_handler_handle_plugin_command(ctx, message);
            
        default:
            LOG_WARN_MSG("Unknown message type received");
            break;
    }
    
    return AGENT_SUCCESS;
}

AgentResult message_handler_send_status(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    char status_buffer[2048];
    snprintf(status_buffer, sizeof(status_buffer),
        "{"
        "\"agent_id\":\"%s\","
        "\"state\":\"%s\","
        "\"uptime\":%llu,"
        "\"plugin_count\":%d,"
        "\"memory_usage\":%lu,"
        "\"cpu_usage\":%.2f"
        "}",
        ctx->config.agent_id,
        (ctx->state == AGENT_STATE_CONNECTED) ? "connected" : "disconnected",
        get_timestamp_ms() - ctx->stats.start_time,
        ctx->plugin_count,
        ctx->stats.memory_usage,
        ctx->stats.cpu_usage);
    
    char* message = create_json_message("status", ctx->config.agent_id, status_buffer);
    if (!message) {
        return AGENT_ERROR_MEMORY;
    }
    
    AgentResult result = ws_client_send_message(ctx, message);
    free_json_message(message);
    
    return result;
}

AgentResult message_handler_handle_command(AgentContext* ctx, const char* command, const char* params)
{
    if (!ctx || !command) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_INFO_MSG("Handling command: %s", command);
    
    if (strcmp(command, "reload_plugins") == 0) {
        return plugin_manager_scan_directory(ctx);
    }
    else if (strcmp(command, "get_plugin_status") == 0) {
        char status_buffer[2048];
        plugin_manager_get_status(ctx, status_buffer, sizeof(status_buffer));
        
        char* message = create_json_message("plugin_status", ctx->config.agent_id, status_buffer);
        if (message) {
            AgentResult result = ws_client_send_message(ctx, message);
            free_json_message(message);
            return result;
        }
        return AGENT_ERROR_MEMORY;
    }
    else if (strcmp(command, "shutdown") == 0) {
        LOG_INFO_MSG("Shutdown command received");
        ctx->state = AGENT_STATE_SHUTDOWN;
        return AGENT_SUCCESS;
    }
    else {
        LOG_WARN_MSG("Unknown command: %s", command);
        return AGENT_ERROR_INVALID_PARAM;
    }
}

MessageType parse_message_type(const char* message)
{
    if (!message) {
        return MSG_TYPE_UNKNOWN;
    }
    
    char* type = extract_json_field(message, "type");
    if (!type) {
        return MSG_TYPE_UNKNOWN;
    }
    
    MessageType result = MSG_TYPE_UNKNOWN;
    
    if (strcmp(type, "heartbeat") == 0) {
        result = MSG_TYPE_HEARTBEAT;
    } else if (strcmp(type, "system_info") == 0) {
        result = MSG_TYPE_SYSTEM_INFO;
    } else if (strcmp(type, "plugin_data") == 0) {
        result = MSG_TYPE_PLUGIN_DATA;
    } else if (strcmp(type, "status") == 0) {
        result = MSG_TYPE_STATUS;
    } else if (strcmp(type, "command") == 0) {
        result = MSG_TYPE_COMMAND;
    } else if (strcmp(type, "plugin_download") == 0) {
        result = MSG_TYPE_PLUGIN_DOWNLOAD;
    } else if (strcmp(type, "plugin_execute") == 0) {
        result = MSG_TYPE_PLUGIN_EXECUTE;
    } else if (strcmp(type, "plugin_command") == 0) {
        result = MSG_TYPE_PLUGIN_COMMAND;
    } else if (strcmp(type, "error") == 0) {
        result = MSG_TYPE_ERROR;
    }
    
    free(type);
    return result;
}

AgentResult message_handler_handle_plugin_download(AgentContext* ctx, const char* message)
{
    if (!ctx || !message) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Extract plugin information from message
    char* plugin_id = extract_json_field(message, "pluginId");
    char* plugin_name = extract_json_field(message, "pluginName");
    char* plugin_data = extract_json_field(message, "pluginData");
    char* checksum = extract_json_field(message, "checksum");
    char* auto_execute_str = extract_json_field(message, "autoExecute");
    
    if (!plugin_id || !plugin_name || !plugin_data || !checksum) {
        LOG_ERROR_MSG("Missing required fields in plugin download message");
        
        if (plugin_id) free(plugin_id);
        if (plugin_name) free(plugin_name);
        if (plugin_data) free(plugin_data);
        if (checksum) free(checksum);
        if (auto_execute_str) free(auto_execute_str);
        
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    int auto_execute = (auto_execute_str && strcmp(auto_execute_str, "true") == 0) ? 1 : 0;
    
    LOG_INFO_MSG("Downloading plugin: %s (ID: %s)", plugin_name, plugin_id);
    
    // Download and install plugin
    AgentResult result = plugin_manager_download_plugin(ctx, plugin_id, plugin_name, 
                                                       plugin_data, checksum, auto_execute);
    
    // Send acknowledgment
    char ack_data[256];
    if (result == AGENT_SUCCESS) {
        snprintf(ack_data, sizeof(ack_data), 
                "{\"success\":true,\"pluginId\":\"%s\",\"message\":\"Plugin downloaded successfully\"}", 
                plugin_id);
    } else {
        snprintf(ack_data, sizeof(ack_data), 
                "{\"success\":false,\"pluginId\":\"%s\",\"message\":\"Plugin download failed\"}", 
                plugin_id);
    }
    
    char* ack_message = create_json_message("ack", ctx->config.agent_id, ack_data);
    if (ack_message) {
        ws_client_send_message(ctx, ack_message);
        free_json_message(ack_message);
    }
    
    // Cleanup
    free(plugin_id);
    free(plugin_name);
    free(plugin_data);
    free(checksum);
    if (auto_execute_str) free(auto_execute_str);
    
    return result;
}

AgentResult message_handler_handle_plugin_execute(AgentContext* ctx, const char* message)
{
    if (!ctx || !message) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    char* plugin_id = extract_json_field(message, "pluginId");
    if (!plugin_id) {
        LOG_ERROR_MSG("Missing pluginId in plugin execute message");
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_INFO_MSG("Executing plugin: %s", plugin_id);
    
    AgentResult result = plugin_manager_execute_plugin_by_id(ctx, plugin_id);
    
    // Send acknowledgment
    char ack_data[256];
    if (result == AGENT_SUCCESS) {
        snprintf(ack_data, sizeof(ack_data), 
                "{\"success\":true,\"pluginId\":\"%s\",\"message\":\"Plugin executed successfully\"}", 
                plugin_id);
    } else {
        snprintf(ack_data, sizeof(ack_data), 
                "{\"success\":false,\"pluginId\":\"%s\",\"message\":\"Plugin execution failed\"}", 
                plugin_id);
    }
    
    char* ack_message = create_json_message("ack", ctx->config.agent_id, ack_data);
    if (ack_message) {
        ws_client_send_message(ctx, ack_message);
        free_json_message(ack_message);
    }
    
    free(plugin_id);
    return result;
}

char* extract_json_field(const char* json, const char* field)
{
    if (!json || !field) {
        return NULL;
    }
    
    char search_pattern[128];
    snprintf(search_pattern, sizeof(search_pattern), "\"%s\":\"", field);
    
    char* start = strstr(json, search_pattern);
    if (!start) {
        return NULL;
    }
    
    start += strlen(search_pattern);
    char* end = strchr(start, '"');
    if (!end) {
        return NULL;
    }
    
    size_t length = end - start;
    char* result = malloc(length + 1);
    if (!result) {
        return NULL;
    }
    
    memcpy(result, start, length);
    result[length] = '\0';
    
    return result;
}

// 处理插件命令消息
AgentResult message_handler_handle_plugin_command(AgentContext* ctx, const char* message)
{
    if (!ctx || !message) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // 提取插件名称和命令数据 - 先获取data字段
    char* data_field = extract_json_field(message, "data");
    if (!data_field) {
        LOG_ERROR_MSG("Missing data field in plugin command message");
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    char* plugin_name = extract_json_field(data_field, "pluginName");
    char* command_data = extract_json_field(data_field, "commandData");
    
    if (!plugin_name || !command_data) {
        LOG_ERROR_MSG("Missing required fields in plugin command message");
        if (plugin_name) free(plugin_name);
        if (command_data) free(command_data);
        free(data_field);
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_INFO_MSG("Executing plugin command for: %s", plugin_name);
    
    // 查找插件
    PluginInfo* plugin = NULL;
    for (int i = 0; i < ctx->plugin_count; i++) {
        if (strcmp(ctx->plugins[i].name, plugin_name) == 0 && 
            ctx->plugins[i].state == PLUGIN_STATE_LOADED) {
            plugin = &ctx->plugins[i];
            break;
        }
    }
    
    if (!plugin) {
        LOG_ERROR_MSG("Plugin not found or not loaded: %s", plugin_name);
        free(plugin_name);
        free(command_data);
        free(data_field);
        return AGENT_ERROR_NOT_FOUND;
    }
    
    // 调用插件的命令处理函数
    if (plugin->handle) {
        // 查找插件的命令处理函数
        typedef char* (*plugin_handle_command_func)(const char*);
        plugin_handle_command_func handle_command = 
            (plugin_handle_command_func)GetProcAddress(plugin->handle, "plugin_handle_command");
        
        if (handle_command) {
            char* result = handle_command(command_data);
            if (result) {
                LOG_INFO_MSG("Plugin command executed successfully, result: %.100s", result);
                
                // 这里可以发送结果回服务器（如果需要）
                // ws_client_send_message(ctx, result);
                
                free(result);
            } else {
                LOG_ERROR_MSG("Plugin command execution failed");
            }
        } else {
            LOG_ERROR_MSG("Plugin does not support command handling: %s", plugin_name);
        }
    }
    
    free(plugin_name);
    free(command_data);
    free(data_field);
    
    return AGENT_SUCCESS;
}