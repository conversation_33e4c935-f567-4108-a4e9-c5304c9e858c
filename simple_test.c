#include <stdio.h>
#include <windows.h>

int main() {
    printf("Testing file manager plugin loading...\n");
    
    // 尝试加载DLL
    HMODULE handle = LoadLibraryA(".\\agent_full\\plugins\\file_manager.dll");
    if (handle) {
        printf("✅ Plugin loaded successfully!\n");
        
        // 尝试获取导出函数
        void* init_func = GetProcAddress(handle, "plugin_init");
        void* handle_func = GetProcAddress(handle, "plugin_handle_command");
        
        if (init_func && handle_func) {
            printf("✅ Required functions found!\n");
        } else {
            printf("❌ Required functions not found\n");
        }
        
        FreeLibrary(handle);
    } else {
        printf("❌ Failed to load plugin DLL\n");
        printf("Error code: %lu\n", GetLastError());
    }
    
    return 0;
}