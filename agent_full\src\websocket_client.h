#ifndef WEBSOCKET_CLIENT_H
#define WEBSOCKET_CLIENT_H

#include "agent.h"

// WebSocket client functions
AgentResult ws_client_init(void);
void ws_client_cleanup(void);
AgentResult ws_client_connect(AgentContext* ctx);
AgentResult ws_client_disconnect(AgentContext* ctx);
AgentResult ws_client_send_message(AgentContext* ctx, const char* message);
AgentResult ws_client_receive_message(AgentContext* ctx, char* buffer, size_t buffer_size, size_t* received_size);
bool ws_client_is_connected(const AgentContext* ctx);

// Helper functions
AgentResult ws_client_send_heartbeat(AgentContext* ctx);
AgentResult ws_client_send_system_info(AgentContext* ctx);

#endif // WEBSOCKET_CLIENT_H