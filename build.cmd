@echo off
echo Building Distributed Monitoring System...
echo.

echo 1. Building Agent...
cd agent_full
if not exist build mkdir build
cd build
cmake .. && cmake --build . --config Release
if %ERRORLEVEL% NEQ 0 (
    echo Agent build failed!
    pause
    exit /b 1
)
cd ..\..

echo.
echo 2. Building Server...
cd server
call npm install
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo Server build failed!
    pause
    exit /b 1
)
cd ..

echo.
echo Build completed successfully!
echo.
echo To start the system:
echo 1. Run server: cd server && npm start
echo 2. Run agent: agent_full\build\Release\agent.exe
echo.
pause