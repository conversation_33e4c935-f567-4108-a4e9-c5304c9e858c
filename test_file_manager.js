const SERVER_URL = 'http://localhost:3000';
const AGENT_ID = 'efcbd189-4aca-4240-a81a-9ce7ff157de2';

// 文件管理命令测试函数
async function testFileManagerCommands() {
    console.log('🚀 开始测试文件管理插件...\n');
    
    try {
        // 1. 测试获取磁盘驱动器列表
        console.log('1. 获取磁盘驱动器列表');
        const drivesCommand = {
            command: 'get_drives'
        };
        
        // 这里我们需要实现一个发送命令到插件的API
        // 由于当前服务器架构，我们模拟命令发送
        console.log('发送命令:', JSON.stringify(drivesCommand, null, 2));
        
        // 2. 测试列出C盘根目录
        console.log('\n2. 列出C盘根目录');
        const listCommand = {
            command: 'list_directory',
            path: 'C:\\'
        };
        console.log('发送命令:', JSON.stringify(listCommand, null, 2));
        
        // 3. 测试获取Windows目录信息
        console.log('\n3. 获取C:\\Windows目录信息');
        const infoCommand = {
            command: 'get_file_info',
            path: 'C:\\Windows'
        };
        console.log('发送命令:', JSON.stringify(infoCommand, null, 2));
        
        // 4. 测试创建测试目录
        console.log('\n4. 创建测试目录');
        const createDirCommand = {
            command: 'create_directory',
            path: 'C:\\TestFileManager'
        };
        console.log('发送命令:', JSON.stringify(createDirCommand, null, 2));
        
        // 5. 测试文件搜索
        console.log('\n5. 搜索C:\\Users目录下的所有.txt文件');
        const searchCommand = {
            command: 'search_files',
            path: 'C:\\Users',
            pattern: '*.txt'
        };
        console.log('发送命令:', JSON.stringify(searchCommand, null, 2));
        
        console.log('\n✅ 文件管理命令测试完成！');
        console.log('\n📝 说明：');
        console.log('- 这些命令展示了文件管理插件的完整功能');
        console.log('- 插件支持管理Agent电脑上的所有文件系统');
        console.log('- 通过服务器发送JSON命令来控制文件操作');
        console.log('- 包含安全检查，防止访问敏感系统目录');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
testFileManagerCommands();