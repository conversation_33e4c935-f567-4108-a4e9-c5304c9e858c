# 分布式监控系统插件开发指南

## 概述

本文档提供了为分布式监控系统开发插件的完整指南。插件是动态加载的库文件，用于扩展Agent的功能，支持自定义监控、数据收集和处理任务。

## 插件架构

### 支持的平台
- **Windows**: `.dll` 文件
- **Linux**: `.so` 文件  
- **macOS**: `.dylib` 文件

### 插件生命周期
1. **初始化** (`plugin_init`) - 插件加载时调用
2. **执行** (`plugin_execute`) - 定期或按需执行
3. **数据获取** (`plugin_get_data`) - 返回插件收集的数据
4. **清理** (`plugin_cleanup`) - 插件卸载时调用

## 必需的接口函数

每个插件必须实现以下4个标准接口函数：

### 1. plugin_init()
插件初始化函数，在插件加载时调用。

```c
__declspec(dllexport) int plugin_init(void);
```

**返回值**:
- `0`: 初始化成功
- `非0`: 初始化失败

**作用**:
- 分配资源
- 初始化配置
- 设置内部状态

### 2. plugin_execute()
插件主要执行函数，包含插件的核心逻辑。

```c
__declspec(dllexport) int plugin_execute(void);
```

**返回值**:
- `0`: 执行成功
- `非0`: 执行失败

**作用**:
- 执行监控任务
- 收集数据
- 处理业务逻辑

### 3. plugin_get_data()
获取插件数据函数，返回插件收集的数据。

```c
__declspec(dllexport) char* plugin_get_data(void);
```

**返回值**:
- `char*`: JSON格式的数据字符串
- `NULL`: 无数据或错误

**注意**:
- 返回的字符串必须是动态分配的（使用malloc）
- 调用方负责释放内存（使用free）
- 数据格式必须是有效的JSON

### 4. plugin_cleanup()
插件清理函数，在插件卸载时调用。

```c
__declspec(dllexport) int plugin_cleanup(void);
```

**返回值**:
- `0`: 清理成功
- `非0`: 清理失败

**作用**:
- 释放分配的资源
- 关闭文件句柄
- 清理临时文件

## 插件模板

### Windows DLL 插件模板

```c
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <time.h>

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

// 插件初始化函数
__declspec(dllexport) int plugin_init(void)
{
    // 在这里添加初始化代码
    // 例如：分配内存、打开文件、初始化配置等
    
    return 0; // 返回0表示成功
}

// 插件执行函数
__declspec(dllexport) int plugin_execute(void)
{
    // 在这里添加插件的主要逻辑
    // 例如：收集系统信息、监控文件变化、执行检查等
    
    return 0; // 返回0表示执行成功
}

// 插件清理函数
__declspec(dllexport) int plugin_cleanup(void)
{
    // 在这里添加清理代码
    // 例如：释放内存、关闭文件、清理临时数据等
    
    return 0; // 返回0表示清理成功
}

// 获取插件数据函数
__declspec(dllexport) char* plugin_get_data(void)
{
    // 分配内存用于返回数据
    char* data = malloc(512);
    if (!data) {
        return NULL;
    }
    
    // 生成JSON格式的数据
    time_t now = time(NULL);
    snprintf(data, 512, 
        "{"
        "\"plugin_name\":\"my_plugin\","
        "\"version\":\"1.0.0\","
        "\"timestamp\":%lld,"
        "\"status\":\"running\","
        "\"data\":{"
            "\"custom_field\":\"custom_value\""
        "}"
        "}", (long long)now);
    
    return data; // 调用方负责释放这个内存
}
```

### Linux SO 插件模板

```c
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

// 插件初始化函数
int plugin_init(void)
{
    // 初始化代码
    return 0;
}

// 插件执行函数
int plugin_execute(void)
{
    // 主要逻辑
    return 0;
}

// 插件清理函数
int plugin_cleanup(void)
{
    // 清理代码
    return 0;
}

// 获取插件数据函数
char* plugin_get_data(void)
{
    char* data = malloc(512);
    if (!data) {
        return NULL;
    }
    
    time_t now = time(NULL);
    snprintf(data, 512, 
        "{"
        "\"plugin_name\":\"my_plugin\","
        "\"version\":\"1.0.0\","
        "\"timestamp\":%ld,"
        "\"status\":\"running\""
        "}", now);
    
    return data;
}
```

## 开发指南

### 1. 环境设置

#### Windows 开发环境
- **编译器**: Visual Studio 2019/2022 或 MinGW
- **工具链**: CMake 3.16+
- **SDK**: Windows SDK 10.0+

#### Linux 开发环境
- **编译器**: GCC 7.0+ 或 Clang 6.0+
- **工具链**: CMake 3.16+, Make
- **依赖**: 标准C库

### 2. 编译配置

#### CMakeLists.txt 示例

```cmake
cmake_minimum_required(VERSION 3.16)
project(MyPlugin VERSION 1.0.0 LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 编译选项
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -fPIC)
endif()

# 创建共享库
add_library(my_plugin SHARED my_plugin.c)

# Windows特定配置
if(WIN32)
    set_target_properties(my_plugin PROPERTIES
        PREFIX ""
        SUFFIX ".dll"
    )
endif()

# Linux特定配置
if(UNIX AND NOT APPLE)
    set_target_properties(my_plugin PROPERTIES
        PREFIX ""
        SUFFIX ".so"
    )
endif()

# macOS特定配置
if(APPLE)
    set_target_properties(my_plugin PROPERTIES
        PREFIX ""
        SUFFIX ".dylib"
    )
endif()
```

#### 编译命令
```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake ..

# 编译
cmake --build . --config Release
```

### 3. 数据格式规范

插件返回的数据必须是有效的JSON格式，建议包含以下标准字段：

```json
{
    "plugin_name": "插件名称",
    "version": "版本号",
    "timestamp": 1754224136359,
    "status": "running|stopped|error",
    "data": {
        // 插件特定的数据
    },
    "metadata": {
        "execution_time": 150,
        "last_update": "2025-08-03T12:30:00Z"
    }
}
```

### 4. 错误处理

#### 错误代码约定
- `0`: 操作成功
- `1`: 一般错误
- `2`: 内存分配失败
- `3`: 文件操作失败
- `4`: 网络错误
- `5`: 配置错误

#### 错误处理示例
```c
int plugin_execute(void)
{
    FILE* file = fopen("data.txt", "r");
    if (!file) {
        // 记录错误信息
        return 3; // 文件操作失败
    }
    
    // 正常处理
    fclose(file);
    return 0;
}
```

### 5. 内存管理

#### 重要原则
1. **动态分配**: `plugin_get_data()` 返回的字符串必须用 `malloc()` 分配
2. **调用方释放**: Agent负责释放返回的内存
3. **避免泄漏**: 在 `plugin_cleanup()` 中释放所有内部资源

#### 内存管理示例
```c
// 全局变量示例
static char* internal_buffer = NULL;

int plugin_init(void)
{
    // 分配内部缓冲区
    internal_buffer = malloc(1024);
    if (!internal_buffer) {
        return 2; // 内存分配失败
    }
    return 0;
}

char* plugin_get_data(void)
{
    // 为返回值分配新内存
    char* result = malloc(256);
    if (!result) {
        return NULL;
    }
    
    // 填充数据
    strcpy(result, "{\"status\":\"ok\"}");
    return result; // Agent会释放这个内存
}

int plugin_cleanup(void)
{
    // 释放内部资源
    if (internal_buffer) {
        free(internal_buffer);
        internal_buffer = NULL;
    }
    return 0;
}
```

## 插件类型示例

### 1. 系统监控插件

```c
#include <windows.h>
#include <psapi.h>
#include <stdio.h>
#include <stdlib.h>

// 获取系统CPU和内存使用率
char* plugin_get_data(void)
{
    char* data = malloc(512);
    if (!data) return NULL;
    
    // 获取内存信息
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(memInfo);
    GlobalMemoryStatusEx(&memInfo);
    
    double memoryUsage = (double)(memInfo.ullTotalPhys - memInfo.ullAvailPhys) / memInfo.ullTotalPhys * 100;
    
    snprintf(data, 512,
        "{"
        "\"plugin_name\":\"system_monitor\","
        "\"timestamp\":%lld,"
        "\"data\":{"
            "\"memory_usage\":%.2f,"
            "\"total_memory\":%llu,"
            "\"free_memory\":%llu"
        "}"
        "}", (long long)time(NULL), memoryUsage, memInfo.ullTotalPhys, memInfo.ullAvailPhys);
    
    return data;
}
```

### 2. 文件监控插件

```c
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>

static char monitored_file[] = "C:\\important\\file.txt";
static FILETIME last_write_time = {0};

int plugin_execute(void)
{
    HANDLE hFile = CreateFileA(monitored_file, GENERIC_READ, 
                              FILE_SHARE_READ, NULL, OPEN_EXISTING, 
                              FILE_ATTRIBUTE_NORMAL, NULL);
    
    if (hFile == INVALID_HANDLE_VALUE) {
        return 3; // 文件访问失败
    }
    
    FILETIME writeTime;
    if (GetFileTime(hFile, NULL, NULL, &writeTime)) {
        // 检查文件是否被修改
        if (CompareFileTime(&writeTime, &last_write_time) > 0) {
            last_write_time = writeTime;
            // 文件已被修改，记录事件
        }
    }
    
    CloseHandle(hFile);
    return 0;
}

char* plugin_get_data(void)
{
    char* data = malloc(256);
    if (!data) return NULL;
    
    snprintf(data, 256,
        "{"
        "\"plugin_name\":\"file_monitor\","
        "\"monitored_file\":\"%s\","
        "\"last_modified\":%lld"
        "}", monitored_file, (long long)time(NULL));
    
    return data;
}
```

### 3. 网络监控插件

```c
#include <winsock2.h>
#include <iphlpapi.h>
#include <stdio.h>
#include <stdlib.h>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")

char* plugin_get_data(void)
{
    char* data = malloc(512);
    if (!data) return NULL;
    
    MIB_IFTABLE* pIfTable = NULL;
    DWORD dwSize = 0;
    
    // 获取网络接口信息
    if (GetIfTable(pIfTable, &dwSize, FALSE) == ERROR_INSUFFICIENT_BUFFER) {
        pIfTable = malloc(dwSize);
        if (pIfTable && GetIfTable(pIfTable, &dwSize, FALSE) == NO_ERROR) {
            DWORD totalBytesIn = 0, totalBytesOut = 0;
            
            for (DWORD i = 0; i < pIfTable->dwNumEntries; i++) {
                totalBytesIn += pIfTable->table[i].dwInOctets;
                totalBytesOut += pIfTable->table[i].dwOutOctets;
            }
            
            snprintf(data, 512,
                "{"
                "\"plugin_name\":\"network_monitor\","
                "\"interfaces\":%lu,"
                "\"bytes_received\":%lu,"
                "\"bytes_sent\":%lu"
                "}", pIfTable->dwNumEntries, totalBytesIn, totalBytesOut);
        }
        
        if (pIfTable) free(pIfTable);
    }
    
    return data;
}
```

## 调试和测试

### 1. 本地测试

创建测试程序来验证插件功能：

```c
// test_plugin.c
#include <stdio.h>
#include <stdlib.h>
#include <windows.h>

int main()
{
    // 加载插件DLL
    HMODULE hPlugin = LoadLibrary(L"my_plugin.dll");
    if (!hPlugin) {
        printf("Failed to load plugin\n");
        return 1;
    }
    
    // 获取函数指针
    int (*init_func)(void) = (int(*)(void))GetProcAddress(hPlugin, "plugin_init");
    int (*execute_func)(void) = (int(*)(void))GetProcAddress(hPlugin, "plugin_execute");
    char* (*get_data_func)(void) = (char*(*)(void))GetProcAddress(hPlugin, "plugin_get_data");
    int (*cleanup_func)(void) = (int(*)(void))GetProcAddress(hPlugin, "plugin_cleanup");
    
    if (!init_func || !execute_func || !get_data_func || !cleanup_func) {
        printf("Failed to find plugin functions\n");
        FreeLibrary(hPlugin);
        return 1;
    }
    
    // 测试插件
    printf("Initializing plugin...\n");
    if (init_func() != 0) {
        printf("Plugin initialization failed\n");
        FreeLibrary(hPlugin);
        return 1;
    }
    
    printf("Executing plugin...\n");
    if (execute_func() != 0) {
        printf("Plugin execution failed\n");
        cleanup_func();
        FreeLibrary(hPlugin);
        return 1;
    }
    
    printf("Getting plugin data...\n");
    char* data = get_data_func();
    if (data) {
        printf("Plugin data: %s\n", data);
        free(data);
    }
    
    printf("Cleaning up plugin...\n");
    cleanup_func();
    FreeLibrary(hPlugin);
    
    printf("Plugin test completed successfully\n");
    return 0;
}
```

### 2. 日志记录

在插件中添加日志功能：

```c
#include <stdio.h>
#include <time.h>

void plugin_log(const char* level, const char* message)
{
    FILE* log_file = fopen("plugin.log", "a");
    if (log_file) {
        time_t now = time(NULL);
        char* time_str = ctime(&now);
        time_str[strlen(time_str) - 1] = '\0'; // 移除换行符
        
        fprintf(log_file, "[%s] %s: %s\n", time_str, level, message);
        fclose(log_file);
    }
}

int plugin_execute(void)
{
    plugin_log("INFO", "Plugin execution started");
    
    // 执行逻辑
    
    plugin_log("INFO", "Plugin execution completed");
    return 0;
}
```

## 最佳实践

### 1. 性能优化
- 避免在 `plugin_execute()` 中执行耗时操作
- 使用缓存减少重复计算
- 合理使用多线程（注意线程安全）

### 2. 错误处理
- 始终检查返回值和NULL指针
- 提供有意义的错误代码
- 在错误情况下正确清理资源

### 3. 兼容性
- 使用标准C库函数
- 避免使用平台特定的API（除非必要）
- 测试不同的操作系统版本

### 4. 安全考虑
- 验证输入参数
- 避免缓冲区溢出
- 不要硬编码敏感信息
- 使用安全的字符串函数

### 5. 文档化
- 为每个函数添加注释
- 说明插件的功能和限制
- 提供使用示例

## 部署流程

### 1. 编译和打包
```bash
# 编译插件
cmake --build . --config Release

# 创建部署包
mkdir deploy
cp Release/my_plugin.dll deploy/
cp plugin_config.json deploy/
cp README.txt deploy/
```

### 2. 上传到服务器
```bash
curl -X POST \
  -F "plugin=@my_plugin.dll" \
  -F "name=my_plugin" \
  -F "version=1.0.0" \
  -F "description=My custom plugin" \
  -F "author=Developer" \
  -F "category=custom" \
  http://localhost:3000/api/plugins/upload
```

### 3. 审核和分发
```bash
# 审核插件
curl -X POST -H "Content-Type: application/json" \
  -d '{"approved":true,"reviewer":"Admin","notes":"Approved for production"}' \
  http://localhost:3000/api/plugins/{pluginId}/audit

# 分发插件
curl -X POST -H "Content-Type: application/json" \
  -d '{"agentId":"target-agent-id","autoExecute":true}' \
  http://localhost:3000/api/plugins/{pluginId}/distribute
```

## 故障排除

### 常见问题

1. **插件加载失败**
   - 检查导出函数名称是否正确
   - 验证DLL依赖是否满足
   - 确认目标平台架构匹配

2. **内存泄漏**
   - 确保在 `plugin_cleanup()` 中释放所有资源
   - 检查 `plugin_get_data()` 返回的内存是否正确分配

3. **数据格式错误**
   - 验证返回的JSON格式是否有效
   - 确保字符串正确转义

4. **性能问题**
   - 减少 `plugin_execute()` 的执行时间
   - 优化数据收集算法
   - 考虑异步处理

### 调试技巧

1. **使用调试器**
   - Visual Studio调试器（Windows）
   - GDB（Linux）
   - 设置断点检查执行流程

2. **添加详细日志**
   - 记录函数调用
   - 输出中间变量值
   - 记录错误详情

3. **单元测试**
   - 为每个函数编写测试用例
   - 测试边界条件
   - 验证内存管理

## 示例项目

完整的示例项目可以在以下位置找到：
- `examples/system_monitor_plugin/` - 系统监控插件
- `examples/file_watcher_plugin/` - 文件监控插件
- `examples/network_scanner_plugin/` - 网络扫描插件

每个示例包含：
- 完整的源代码
- CMakeLists.txt配置
- 详细的README
- 测试脚本

通过学习这些示例，您可以快速上手插件开发，并将其应用到实际的监控需求中。

---

本文档将持续更新以反映最新的API变化和最佳实践。如有疑问，请参考API文档或联系开发团队。