cmake_minimum_required(VERSION 3.16)
project(FileMonitorPlugin VERSION 1.0.0 LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 编译选项
if(MSVC)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /W4")
    set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} /O2 /DNDEBUG")
else()
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")
    set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -g -O0")
    set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# 包含目录
include_directories(../../agent/src/include)
include_directories(include)

# 源文件
set(PLUGIN_SOURCES
    src/file_monitor_plugin.c
    src/file_watcher.c
    src/file_analyzer.c
    src/file_filter.c
)

# 头文件
set(PLUGIN_HEADERS
    include/file_monitor_plugin.h
    include/file_watcher.h
    include/file_analyzer.h
    include/file_filter.h
)

# Windows特定配置
if(WIN32)
    # Windows库
    set(PLATFORM_LIBS
        kernel32
        user32
        advapi32
        shell32
        shlwapi
    )
    
    # Windows定义
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    add_definitions(-DUNICODE)
    add_definitions(-D_UNICODE)
endif()

# 创建DLL
add_library(file_monitor SHARED ${PLUGIN_SOURCES} ${PLUGIN_HEADERS})

# 链接库
target_link_libraries(file_monitor ${PLATFORM_LIBS})

# 设置DLL属性
set_target_properties(file_monitor PROPERTIES
    PREFIX ""
    SUFFIX ".dll"
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 导出所有符号
if(WIN32)
    set_target_properties(file_monitor PROPERTIES WINDOWS_EXPORT_ALL_SYMBOLS TRUE)
endif()

# 版本信息
set_target_properties(file_monitor PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 安装规则
install(TARGETS file_monitor 
        RUNTIME DESTINATION bin
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib)

# 调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "Building File Monitor Plugin in Debug mode")
    add_definitions(-DDEBUG)
    add_definitions(-DENABLE_LOGGING)
else()
    message(STATUS "Building File Monitor Plugin in Release mode")
    add_definitions(-DRELEASE)
endif()

message(STATUS "File Monitor Plugin version: ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Platform libraries: ${PLATFORM_LIBS}")